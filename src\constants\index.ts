// Application Constants
export const APP_CONFIG = {
  name: 'Job Tracker Extension',
  version: '3.0',
  description: 'AI-powered job application tracker',
} as const;

// API Configuration
export const API_CONFIG = {
  baseUrl: 'http://*************:8001/api/extension',
  ollamaUrl: 'http://**************:11434',
  timeout: 30000,
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  authToken: 'authToken',
  selectedAccount: 'selectedAccount',
  hotkey: 'hotkey',
  theme: 'theme',
  lastUsedKeyIndex: 'lastUsedKeyIndex',
  serverUrl: 'serverUrl',
  aiServerUrl: 'aiServerUrl',
} as const;

// Default Values
export const DEFAULTS = {
  hotkey: 'Control+F11',
  theme: 'light',
  timeout: 3000,
  serverUrl: 'http://*************:8001/api/extension',
  aiServerUrl: 'http://**************:11434',
} as const;

// Message Types for Chrome Extension Communication
export const MESSAGE_TYPES = {
  SAVE_JOB: 'SAVE_JOB',
  CHECK_CONTENT_LOADED: 'CHECK_CONTENT_LOADED',
  AI_JOB_PARSE: 'AI_JOB_PARSE',
  HOTKEY_UPDATED: 'HOTKEY_UPDATED',
  SHOW_TOAST: 'SHOW_TOAST',
  SHOW_DIALOG: 'SHOW_DIALOG',
} as const;

// Toast Types
export const TOAST_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  ERROR: 'error',
  LOADING: 'loading',
} as const;

// Setup Stages
export const SETUP_STAGES = {
  LOADING: 'LOADING',
  TOKEN_INPUT: 'TOKEN_INPUT',
  ACCOUNT_SELECTION: 'ACCOUNT_SELECTION',
  DASHBOARD: 'DASHBOARD',
} as const;

// Remote Availability Options
export const REMOTE_OPTIONS = [
  'Remote Only',
  'Hybrid',
  'Remote Possible',
  'On-site',
  'Not Specified',
] as const;

// Supported Job Sites
export const SUPPORTED_DOMAINS = [
  'linkedin.com',
  'indeed.com',
  'glassdoor.com',
  'monster.com',
  'ziprecruiter.com',
] as const;

// AI Model Configuration
export const AI_CONFIG = {
  model: 'gemma3:4b',
  temperature: 0.1,
  maxTokens: 1024,
  maxPromptLength: 6000,
} as const;

// UI Constants
export const UI_CONFIG = {
  toastDuration: 3000,
  animationDuration: 300,
  debounceDelay: 500,
  maxDialogWidth: 800,
  maxDialogHeight: 600,
} as const;
