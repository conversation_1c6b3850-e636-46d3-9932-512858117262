// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

import { ApiResponse } from '../types';

/**
 * Custom error classes for better error handling
 */
export class AppError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, public field?: string) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
  }
}

export class NetworkError extends AppError {
  constructor(message: string, statusCode?: number) {
    super(message, 'NETWORK_ERROR', statusCode);
    this.name = 'NetworkError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 'AUTH_ERROR', 401);
    this.name = 'AuthenticationError';
  }
}

/**
 * Error handler for API responses
 */
export const handleApiError = (error: any): ApiResponse => {
  console.error('API Error:', error);
  
  if (error instanceof NetworkError) {
    return {
      success: false,
      error: error.message,
      message: 'Network connection failed. Please check your internet connection.',
    };
  }
  
  if (error instanceof AuthenticationError) {
    return {
      success: false,
      error: error.message,
      message: 'Authentication failed. Please check your credentials.',
    };
  }
  
  if (error instanceof ValidationError) {
    return {
      success: false,
      error: error.message,
      message: `Validation error: ${error.message}`,
    };
  }
  
  // Generic error handling
  return {
    success: false,
    error: error.message || 'Unknown error occurred',
    message: 'An unexpected error occurred. Please try again.',
  };
};

/**
 * Async error wrapper for better error handling
 */
export const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<ApiResponse<R>> => {
    try {
      const result = await fn(...args);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return handleApiError(error);
    }
  };
};

/**
 * Chrome extension error handler
 */
export const handleChromeError = (operation: string): void => {
  if (chrome.runtime.lastError) {
    console.error(`Chrome ${operation} error:`, chrome.runtime.lastError);
    throw new AppError(
      `Chrome extension error during ${operation}: ${chrome.runtime.lastError.message}`,
      'CHROME_ERROR'
    );
  }
};

/**
 * Retry mechanism for failed operations
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError!;
};

/**
 * Safe JSON parsing with error handling
 */
export const safeJsonParse = <T = any>(json: string, fallback?: T): T | null => {
  try {
    return JSON.parse(json);
  } catch (error) {
    console.warn('JSON parse error:', error);
    return fallback ?? null;
  }
};

/**
 * Log error with context
 */
export const logError = (
  error: Error,
  context: string,
  additionalData?: Record<string, any>
): void => {
  console.group(`🚨 Error in ${context}`);
  console.error('Error:', error);
  console.error('Stack:', error.stack);
  if (additionalData) {
    console.error('Additional data:', additionalData);
  }
  console.groupEnd();
};
