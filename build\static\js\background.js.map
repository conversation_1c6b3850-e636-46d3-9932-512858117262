{"version": 3, "file": "static/js/background.js", "mappings": "mBACO,MAcMA,EACA,YADAA,EAEM,kBAFNA,EAMA,YANAA,EAOE,cAIFC,EAIA,0CAJAA,EAKE,8BAIFC,EACD,WADCA,EAEW,uBAFXA,EAGG,eAHHA,EAIK,iBAJLA,EAKC,aAuCDC,EACJ,YADIA,EAEE,GCxER,MAAMC,UAAiBC,MAC5BC,WAAAA,CACEC,EACOC,EACAC,GAEPC,MAAMH,GAAS,KAHRC,KAAAA,EAAa,KACbC,WAAAA,EAGPE,KAAKC,KAAO,UACd,EA2BK,MChCDC,EAAiBC,MAAUC,EAAaC,KAC1C,IAAK,IAADC,EACA,MAAMC,QAAeC,OAAOC,QAAQC,MAAMC,IAAI,CAACP,IAE/C,MDqF0BQ,KAChC,GAAIJ,OAAOK,QAAQC,UAEjB,MADAC,QAAQC,MAAO,UAASJ,WAAoBJ,OAAOK,QAAQC,WACrD,IAAIrB,EACP,iCAAgCmB,MAAcJ,OAAOK,QAAQC,UAAUlB,UACxE,eAEJ,EC7FMqB,CAAkB,eACA,QAAlBX,EAAOC,EAAOH,UAAI,IAAAE,EAAAA,EAAID,CAC1B,CAAE,MAAOW,GAEL,MDuIgBE,EACtBF,EACAG,EACAC,KAEAL,QAAQM,MAAO,yBAAcF,KAC7BJ,QAAQC,MAAM,SAAUA,GACxBD,QAAQC,MAAM,SAAUA,EAAMM,OAC1BF,GACFL,QAAQC,MAAM,mBAAoBI,GAEpCL,QAAQQ,UAAU,ECnJZL,CAASF,EAAiB,kBAAiBZ,MACpCC,CACX,GA8DSmB,EAAerB,UACxB,MAAMI,QAAeL,EAA0Bb,GAC/C,OAAa,OAANkB,QAAM,IAANA,EAAAA,EAAU,IAAI,EA2BZkB,EAAqBtB,UAC9B,MAAMI,QAAeL,EAAwBb,GAC7C,OAAa,OAANkB,QAAM,IAANA,EAAAA,EAAU,IAAI,EA6DZmB,EAAevB,eACXD,EAAuBb,EAAwBC,IAAuBA,ECtKjFqC,EAAgBxB,UAClB,MAAM,IAAEyB,EAAG,QAAEC,EAAO,MAAEC,GAAUC,EAC1BC,EAAU,CACZ,eAAgB,sBACbH,EAAQG,SAGf,IAEI,MAAMC,QAAkBP,IAClBQ,QAAiBC,MAAO,GAAEF,IAAYL,YAAcE,IAAS,IAC5DD,EACHG,YAGJ,IAAKE,EAASE,GAAI,CACd,MAAMC,QAAkBH,EAASI,OACjC,MAAM,IAAI5C,MAAM2C,EAAUzC,SAAW,qBACzC,CAEA,aADmBsC,EAASI,QAChBC,IAChB,CAAE,MAAOvB,GAEL,MADAD,QAAQC,MAAM,qBAAsBA,GAC9BA,CACV,GAISwB,EAQWrC,MAChBsC,EACAC,IAEOf,EAAc,CACjBC,IAAK,eACLC,QAAS,CACLc,OAAQ,OACRC,KAAMC,KAAKC,UAAU,CAAEP,KAAM,IAAIE,EAAYnB,MAAOmB,EAAWM,WAAWC,KAAK,UAEnFlB,MAAOY,EAAUZ,QAlBhBU,EAqBcrC,SACZwB,EAAc,CACjBC,IAAK,sCACLC,QAAS,CAAEc,OAAQ,OACnBb,MAAOY,EAAUZ,QCxD7BtB,OAAOK,QAAQoC,UAAUC,aAAY,CAACtD,EAASuD,EAAQC,KACnD,OAAIxD,EAAQyD,OAAS9D,GACjB+D,EAAQ1D,EAAQ2D,SACXC,MAAKjD,GAAU6C,EAAa7C,KAC5BkD,OAAMzC,IACHD,QAAQC,MAAM,qBAAsBA,GACpCoC,EAAa,CAAEM,SAAS,EAAO9D,QAASoB,EAAMpB,SAAU,KAGzD,GAGPA,EAAQyD,OAAS9D,GACjBiB,OAAOmD,UAAUC,cACb,CACIC,OAAQ,CAAEC,OAAiB,QAAVC,EAAAZ,EAAOa,WAAG,IAAAD,OAAA,EAAVA,EAAYE,KAAM,GACnCC,KAAMA,IAAOC,OAAeC,sBAAuB,IAEtDC,IAIW,IAADC,EAHH9D,OAAOK,QAAQC,WACfC,QAAQC,MAAM,iCAAkCR,OAAOK,QAAQC,WAC/DsC,GAAa,IAEbA,GAAoB,OAAPiB,QAAO,IAAPA,GAAY,QAALC,EAAPD,EAAU,UAAE,IAAAC,OAAL,EAAPA,EAAc/D,UAAU,EACzC,KAGD,GAGPX,EAAQyD,OAAS9D,GACjBgF,EAAiB3E,EAAQ4E,OAAQ5E,EAAQ6E,YACpCjB,MAAKjD,GAAU6C,EAAa7C,KAC5BkD,OAAMzC,IACHD,QAAQC,MAAM,6BAA8BA,GAC5CoC,EAAa,CAAEM,SAAS,EAAO1C,MAAOA,EAAMpB,SAAU,KAGvD,GAGPA,EAAQyD,OAAS9D,GAEjBmF,EAAsB9E,EAAQ+E,SACvB,QAHX,EA7B0D,IAADZ,CAiCzD,IAGJ,MAAMW,EAAwBvE,UAC1B,IACI,MAAMyE,QAAapE,OAAOoE,KAAKC,MAAM,CAAC,GACtC,IAAK,MAAMb,KAAOY,EACVZ,EAAIC,IAAMD,EAAIpC,MAAQoC,EAAIpC,IAAIkD,WAAW,eAAiBd,EAAIpC,IAAIkD,WAAW,wBAC7EtE,OAAOoE,KAAKG,YAAYf,EAAIC,GAAI,CAC5BZ,KAAM9D,EACNoF,OAAQA,IACTlB,OAAM,QAKrB,CAAE,MAAOzC,GACLD,QAAQC,MAAM,oCAAqCA,EACvD,GAGEuD,EAAmBpE,eAAOqE,GAAsD,IAAtCC,EAAUO,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GACtD,IACI,MAAMG,EAAcV,EACd,mFACA,yFAGAW,OFkHgBjF,gBACbD,EAAuBb,EAA0BC,IAAyBA,EEnHzD+F,GACpBnD,QAAiBC,MAAO,GAAEiD,aAAwB,CACpDzC,OAAQ,OACRX,QAAS,CACL,eAAgB,mBAChB,8BAA+B,KAEnCY,KAAMC,KAAKC,UAAU,CACjBwC,MAAO9F,EACP+F,SAAU,CACN,CAAEC,KAAM,SAAUC,QAASN,GAC3B,CAAEK,KAAM,OAAQC,QAASjB,IAE7BkB,YAAalG,EACbmG,QAAQ,MAIhB,IAAKzD,EAASE,GACV,MAAM,IAAI1C,MAAO,uBAAsBwC,EAAS0D,UAIpD,MAAMC,QAAqB3D,EAAS4D,OAGpC,IAAIC,EAFJhF,QAAQiF,IAAI,gBAAiBH,GAG7B,IACIE,EAAalD,KAAKoD,MAAMJ,EAC5B,CAAE,MAAOK,GAEL,MAAMC,EAAQN,EAAaO,OAAOC,MAAM,MAClCC,EAAWH,EAAMA,EAAMlB,OAAS,GACtCc,EAAalD,KAAKoD,MAAMK,EAC5B,CAEA,MAAM1G,EAAUmG,EAAWnG,QAE3B,IAAKA,EACD,MAAM,IAAIF,MAAM,4BAGH,IAAD6G,EAAhB,GAAI9B,EACA,OAAsB,QAAtB8B,EAAO3G,EAAQ6F,eAAO,IAAAc,EAAAA,EAAI,GAG9B,IAAIT,EAAOlG,EAAQ6F,QAAQW,OACvBN,EAAKhB,WAAW,SAChBgB,EAAOA,EAAKU,QAAQ,eAAgB,IAAIA,QAAQ,OAAQ,KAG5D,IAEI,OADY3D,KAAKoD,MAAMH,EAE3B,CAAE,MAAOW,GAEL,MADA1F,QAAQC,MAAM,wBAAyByF,EAAKX,GACtCW,CACV,CACJ,CAAE,MAAOzF,GAEL,MADAD,QAAQC,MAAM,sBAAuBA,GAC/BA,CACV,CACJ,EAEMsC,EAAUnD,UACZ,IACI,MAAMuC,QAAkBlB,IAClBkF,QAAwBjF,IAE9B,OAAKiB,EAIAgE,SAIClE,EACF,IAAKC,EAAYkE,UAAWD,EAAgBE,OAC5ClE,GAGG,CAAEgB,SAAS,EAAM9D,QAAS,2BARtB,CAAE8D,SAAS,EAAO9D,QAAS,uBAJ3B,CAAE8D,SAAS,EAAO9D,QAAS,iCAa1C,CAAE,MAAOoB,GAEL,OADAD,QAAQC,MAAM,oBAAqBA,GAC5B,CACH0C,SAAS,EACT9D,QAASoB,aAAiBtB,MAAQsB,EAAMpB,QAAU,yBAE1D,GAGJY,OAAOK,QAAQgG,YAAY3D,aAAY,KACnCnC,QAAQiF,IAAI,mCACZxF,OAAOsG,aAAaC,OAAO,CACvB9C,GAAI,WACJ+C,MAAO,mCACPC,SAAU,CAAC,SAGfzG,OAAOsG,aAAaC,OAAO,CACvB9C,GAAI,2BACJ+C,MAAO,wBACPC,SAAU,CAAC,cACb,IAGNzG,OAAOsG,aAAaI,UAAUhE,aAAY/C,MAAOgH,EAAMnD,KAC9CA,GAAQA,EAAIC,KACO,aAApBkD,EAAKC,YACL5G,OAAOmD,UAAUC,cAAc,CAC3BC,OAAQ,CAAEC,MAAOE,EAAIC,IACrBC,KAAMA,KACGC,OAAekD,YACflD,OAAekD,YACpB,IAIY,6BAApBF,EAAKC,YAA6CD,EAAKG,qBAsC/DnH,eAA0CoH,EAAsBzD,GAC5D,MAAM0D,EAAY,SAAC5H,GAA4G,IAA3F6H,EAAmDzC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,OAAQ0C,EAAgB1C,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,IACjHxE,OAAOoE,KAAKG,YAAYjB,EAAO,CAC3BT,KAAM9D,EACNK,UACA6H,UAAWA,EACXC,aACDjE,OAAM,QAGb,EAEA,IAEI+D,EAAU,8CAA+C,UAAW,GAEpE,MAAM9E,QAAkBlB,IAClBkF,QAAwBjF,IAE9B,IAAKiB,EAED,YADA8E,EAAU,8DAA+D,SAI7E,IAAKd,EAED,YADAc,EAAU,mDAAoD,SAKlE,MAAMG,QAAkBnF,EAAiCE,GAEzD,IAAKiF,IAAcA,EAAUC,eAEzB,YADAJ,EAAU,qDAAsD,SAIpE,MAAMhD,EAAU,gxBASVmD,EAAUC,oEAEVlB,EAAgBmB,QAAW,iEAE3BnB,EAAgBmB,oFAEeN,cAG/BhH,QAAegE,EAAiBC,GAAQ,GAE9C,IAAKjE,EAED,YADAiH,EAAU,oDAAqD,eAK7DhH,OAAOmD,UAAUC,cAAc,CACjCC,OAAQ,CAAEC,SACVI,KAAO4B,GACIgC,UAAUC,UAAUC,UAAUlC,GAAQ,IAAItC,MAAK,KAC3C,IACRC,OAAMgD,IACL1F,QAAQC,MAAM,0BAA2ByF,IAClC,KAGfwB,KAAM,CAAC1H,KAGXiH,EAAU,iDAAkD,UAEhE,CAAE,MAAOxG,GACLD,QAAQC,MAAM,uCAAwCA,GACtDwG,EAAU,+CAAgD,QAC9D,CACJ,CAvHcU,CAA2Bf,EAAKG,cAAetD,EAAIC,IAC7D,IAwHJlD,QAAQiF,IAAI,uC", "sources": ["constants/index.ts", "utils/errorHandling.ts", "utils/storage.ts", "services/api.ts", "chrome/background.ts"], "sourcesContent": ["// Application Constants\nexport const APP_CONFIG = {\n  name: 'Job Tracker Extension',\n  version: '3.0',\n  description: 'AI-powered job application tracker',\n} as const;\n\n// API Configuration\nexport const API_CONFIG = {\n  baseUrl: 'http://*************:8001/api/extension',\n  ollamaUrl: 'http://**************:11434',\n  timeout: 30000,\n} as const;\n\n// Storage Keys\nexport const STORAGE_KEYS = {\n  authToken: 'authToken',\n  selectedAccount: 'selectedAccount',\n  hotkey: 'hotkey',\n  theme: 'theme',\n  lastUsedKeyIndex: 'lastUsedKeyIndex',\n  serverUrl: 'serverUrl',\n  aiServerUrl: 'aiServerUrl',\n} as const;\n\n// Default Values\nexport const DEFAULTS = {\n  hotkey: 'Control+F11',\n  theme: 'light',\n  timeout: 3000,\n  serverUrl: 'http://*************:8001/api/extension',\n  aiServerUrl: 'http://**************:11434',\n} as const;\n\n// Message Types for Chrome Extension Communication\nexport const MESSAGE_TYPES = {\n  SAVE_JOB: 'SAVE_JOB',\n  CHECK_CONTENT_LOADED: 'CHECK_CONTENT_LOADED',\n  AI_JOB_PARSE: 'AI_JOB_PARSE',\n  HOTKEY_UPDATED: 'HOTKEY_UPDATED',\n  SHOW_TOAST: 'SHOW_TOAST',\n  SHOW_DIALOG: 'SHOW_DIALOG',\n} as const;\n\n// Toast Types\nexport const TOAST_TYPES = {\n  INFO: 'info',\n  SUCCESS: 'success',\n  ERROR: 'error',\n  LOADING: 'loading',\n} as const;\n\n// Setup Stages\nexport const SETUP_STAGES = {\n  LOADING: 'LOADING',\n  TOKEN_INPUT: 'TOKEN_INPUT',\n  ACCOUNT_SELECTION: 'ACCOUNT_SELECTION',\n  DASHBOARD: 'DASHBOARD',\n} as const;\n\n// Remote Availability Options\nexport const REMOTE_OPTIONS = [\n  'Remote Only',\n  'Hybrid',\n  'Remote Possible',\n  'On-site',\n  'Not Specified',\n] as const;\n\n// Supported Job Sites\nexport const SUPPORTED_DOMAINS = [\n  'linkedin.com',\n  'indeed.com',\n  'glassdoor.com',\n  'monster.com',\n  'ziprecruiter.com',\n] as const;\n\n// AI Model Configuration\nexport const AI_CONFIG = {\n  model: 'gemma3:4b',\n  temperature: 0.1,\n  maxTokens: 1024,\n  maxPromptLength: 6000,\n} as const;\n\n// UI Constants\nexport const UI_CONFIG = {\n  toastDuration: 3000,\n  animationDuration: 300,\n  debounceDelay: 500,\n  maxDialogWidth: 800,\n  maxDialogHeight: 600,\n} as const;\n", "// ============================================================================\n// ERROR HANDLING UTILITIES\n// ============================================================================\n\nimport { ApiResponse } from '../types';\n\n/**\n * Custom error classes for better error handling\n */\nexport class AppError extends Error {\n  constructor(\n    message: string,\n    public code?: string,\n    public statusCode?: number\n  ) {\n    super(message);\n    this.name = 'AppError';\n  }\n}\n\nexport class ValidationError extends AppError {\n  constructor(message: string, public field?: string) {\n    super(message, 'VALIDATION_ERROR', 400);\n    this.name = 'ValidationError';\n  }\n}\n\nexport class NetworkError extends AppError {\n  constructor(message: string, statusCode?: number) {\n    super(message, 'NETWORK_ERROR', statusCode);\n    this.name = 'NetworkError';\n  }\n}\n\nexport class AuthenticationError extends AppError {\n  constructor(message: string = 'Authentication failed') {\n    super(message, 'AUTH_ERROR', 401);\n    this.name = 'AuthenticationError';\n  }\n}\n\n/**\n * Error handler for API responses\n */\nexport const handleApiError = (error: any): ApiResponse => {\n  console.error('API Error:', error);\n  \n  if (error instanceof NetworkError) {\n    return {\n      success: false,\n      error: error.message,\n      message: 'Network connection failed. Please check your internet connection.',\n    };\n  }\n  \n  if (error instanceof AuthenticationError) {\n    return {\n      success: false,\n      error: error.message,\n      message: 'Authentication failed. Please check your credentials.',\n    };\n  }\n  \n  if (error instanceof ValidationError) {\n    return {\n      success: false,\n      error: error.message,\n      message: `Validation error: ${error.message}`,\n    };\n  }\n  \n  // Generic error handling\n  return {\n    success: false,\n    error: error.message || 'Unknown error occurred',\n    message: 'An unexpected error occurred. Please try again.',\n  };\n};\n\n/**\n * Async error wrapper for better error handling\n */\nexport const withErrorHandling = <T extends any[], R>(\n  fn: (...args: T) => Promise<R>\n) => {\n  return async (...args: T): Promise<ApiResponse<R>> => {\n    try {\n      const result = await fn(...args);\n      return {\n        success: true,\n        data: result,\n      };\n    } catch (error) {\n      return handleApiError(error);\n    }\n  };\n};\n\n/**\n * Chrome extension error handler\n */\nexport const handleChromeError = (operation: string): void => {\n  if (chrome.runtime.lastError) {\n    console.error(`Chrome ${operation} error:`, chrome.runtime.lastError);\n    throw new AppError(\n      `Chrome extension error during ${operation}: ${chrome.runtime.lastError.message}`,\n      'CHROME_ERROR'\n    );\n  }\n};\n\n/**\n * Retry mechanism for failed operations\n */\nexport const withRetry = async <T>(\n  operation: () => Promise<T>,\n  maxRetries: number = 3,\n  delay: number = 1000\n): Promise<T> => {\n  let lastError: Error;\n  \n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      return await operation();\n    } catch (error) {\n      lastError = error as Error;\n      \n      if (attempt === maxRetries) {\n        throw lastError;\n      }\n      \n      // Wait before retrying\n      await new Promise(resolve => setTimeout(resolve, delay * attempt));\n    }\n  }\n  \n  throw lastError!;\n};\n\n/**\n * Safe JSON parsing with error handling\n */\nexport const safeJsonParse = <T = any>(json: string, fallback?: T): T | null => {\n  try {\n    return JSON.parse(json);\n  } catch (error) {\n    console.warn('JSON parse error:', error);\n    return fallback ?? null;\n  }\n};\n\n/**\n * Log error with context\n */\nexport const logError = (\n  error: Error,\n  context: string,\n  additionalData?: Record<string, any>\n): void => {\n  console.group(`🚨 Error in ${context}`);\n  console.error('Error:', error);\n  console.error('Stack:', error.stack);\n  if (additionalData) {\n    console.error('Additional data:', additionalData);\n  }\n  console.groupEnd();\n};\n", "// ============================================================================\n// IMPROVED STORAGE UTILITIES\n// ============================================================================\n\nimport { AuthToken, Account, ChromeStorage } from \"../types\";\nimport { STORAGE_KEYS, DEFAULTS } from \"../constants\";\nimport { handleChromeError, logError, AppError } from \"./errorHandling\";\nimport { validateAccount, isValidToken } from \"./validation\";\n\n/**\n * Generic storage getter with error handling and type safety\n */\nconst getStorageItem = async <T>(key: string, defaultValue?: T): Promise<T | undefined> => {\n    try {\n        const result = await chrome.storage.local.get([key]);\n        handleChromeError('storage.get');\n        return result[key] ?? defaultValue;\n    } catch (error) {\n        logError(error as Error, `getStorageItem(${key})`);\n        return defaultValue;\n    }\n};\n\n/**\n * Generic storage setter with error handling and validation\n */\nconst setStorageItem = async <T>(key: string, value: T): Promise<void> => {\n    try {\n        await chrome.storage.local.set({ [key]: value });\n        handleChromeError('storage.set');\n        console.info(`✅ Storage item '${key}' saved successfully`);\n    } catch (error) {\n        logError(error as Error, `setStorageItem(${key})`, { value });\n        throw new AppError(`Failed to save ${key} to storage`);\n    }\n};\n\n/**\n * Get all storage data\n */\nexport const getAllStorageData = async (): Promise<ChromeStorage> => {\n    try {\n        const result = await chrome.storage.local.get(null);\n        handleChromeError('storage.get');\n        return result as ChromeStorage;\n    } catch (error) {\n        logError(error as Error, 'getAllStorageData');\n        return {};\n    }\n};\n\n/**\n * Clear all storage data\n */\nexport const clearAllStorage = async (): Promise<void> => {\n    try {\n        await chrome.storage.local.clear();\n        handleChromeError('storage.clear');\n        console.info('✅ All storage data cleared');\n    } catch (error) {\n        logError(error as Error, 'clearAllStorage');\n        throw new AppError('Failed to clear storage');\n    }\n};\n\n// ============================================================================\n// AUTH TOKEN MANAGEMENT\n// ============================================================================\n\nexport const saveAuthToken = async (token: string): Promise<void> => {\n    if (!token?.trim()) {\n        throw new AppError('Token cannot be empty');\n    }\n    \n    if (!isValidToken(token)) {\n        throw new AppError('Invalid token format');\n    }\n    \n    const authToken: AuthToken = { token: token.trim() };\n    await setStorageItem(STORAGE_KEYS.authToken, authToken);\n};\n\nexport const getAuthToken = async (): Promise<AuthToken | null> => {\n    const result = await getStorageItem<AuthToken>(STORAGE_KEYS.authToken);\n    return result ?? null;\n};\n\nexport const removeAuthToken = async (): Promise<void> => {\n    try {\n        await chrome.storage.local.remove([STORAGE_KEYS.authToken]);\n        handleChromeError('storage.remove');\n        console.info('✅ Auth token removed');\n    } catch (error) {\n        logError(error as Error, 'removeAuthToken');\n        throw new AppError('Failed to remove auth token');\n    }\n};\n\n// ============================================================================\n// ACCOUNT MANAGEMENT\n// ============================================================================\n\nexport const saveSelectedAccount = async (account: Account): Promise<void> => {\n    const errors = validateAccount(account);\n    if (errors.length > 0) {\n        throw new AppError(`Invalid account data: ${errors.join(', ')}`);\n    }\n    \n    await setStorageItem(STORAGE_KEYS.selectedAccount, account);\n};\n\nexport const getSelectedAccount = async (): Promise<Account | null> => {\n    const result = await getStorageItem<Account>(STORAGE_KEYS.selectedAccount);\n    return result ?? null;\n};\n\nexport const removeSelectedAccount = async (): Promise<void> => {\n    try {\n        await chrome.storage.local.remove([STORAGE_KEYS.selectedAccount]);\n        handleChromeError('storage.remove');\n        console.info('✅ Selected account removed');\n    } catch (error) {\n        logError(error as Error, 'removeSelectedAccount');\n        throw new AppError('Failed to remove selected account');\n    }\n};\n\n// ============================================================================\n// HOTKEY MANAGEMENT\n// ============================================================================\n\nexport const saveHotkey = async (hotkey: string): Promise<void> => {\n    if (!hotkey?.trim()) {\n        throw new AppError('Hotkey cannot be empty');\n    }\n    \n    await setStorageItem(STORAGE_KEYS.hotkey, hotkey.trim());\n};\n\nexport const getHotkey = async (): Promise<string> => {\n    return await getStorageItem<string>(STORAGE_KEYS.hotkey, DEFAULTS.hotkey) || DEFAULTS.hotkey;\n};\n\n// ============================================================================\n// THEME MANAGEMENT\n// ============================================================================\n\nexport const saveTheme = async (theme: 'light' | 'dark'): Promise<void> => {\n    await setStorageItem(STORAGE_KEYS.theme, theme);\n};\n\nexport const getTheme = async (): Promise<'light' | 'dark'> => {\n    return await getStorageItem<'light' | 'dark'>(STORAGE_KEYS.theme, DEFAULTS.theme) || DEFAULTS.theme;\n};\n\n// ============================================================================\n// SERVER URL MANAGEMENT\n// ============================================================================\n\nexport const saveServerUrl = async (url: string): Promise<void> => {\n    if (!url?.trim()) {\n        throw new AppError('Server URL cannot be empty');\n    }\n\n    // Basic URL validation\n    try {\n        new URL(url.trim());\n    } catch {\n        throw new AppError('Invalid server URL format');\n    }\n\n    await setStorageItem(STORAGE_KEYS.serverUrl, url.trim());\n};\n\nexport const getServerUrl = async (): Promise<string> => {\n    return await getStorageItem<string>(STORAGE_KEYS.serverUrl, DEFAULTS.serverUrl) || DEFAULTS.serverUrl;\n};\n\nexport const saveAiServerUrl = async (url: string): Promise<void> => {\n    if (!url?.trim()) {\n        throw new AppError('AI Server URL cannot be empty');\n    }\n\n    // Basic URL validation\n    try {\n        new URL(url.trim());\n    } catch {\n        throw new AppError('Invalid AI server URL format');\n    }\n\n    await setStorageItem(STORAGE_KEYS.aiServerUrl, url.trim());\n};\n\nexport const getAiServerUrl = async (): Promise<string> => {\n    return await getStorageItem<string>(STORAGE_KEYS.aiServerUrl, DEFAULTS.aiServerUrl) || DEFAULTS.aiServerUrl;\n};\n\n// ============================================================================\n// STORAGE EVENT LISTENERS\n// ============================================================================\n\nexport const onStorageChanged = (\n    callback: (changes: { [key: string]: chrome.storage.StorageChange }) => void\n): void => {\n    chrome.storage.onChanged.addListener((changes, areaName) => {\n        if (areaName === 'local') {\n            callback(changes);\n        }\n    });\n};\n", "import { Application, AuthToken, UserInterface } from \"../types\";\r\nimport { API_CONFIG } from \"../constants\";\r\nimport { getServerUrl } from \"../utils/storage\";\r\n\r\ninterface FetchWithAuthProps {\r\n    url: string;\r\n    options: RequestInit,\r\n    token: string\r\n}\r\nconst fetchWithAuth = async (props: FetchWithAuthProps) => {\r\n    const { url, options, token } = props;\r\n    const headers = {\r\n        'Content-Type': 'application/json',\r\n        ...options.headers\r\n    };\r\n\r\n    try {\r\n        // Get dynamic server URL from storage\r\n        const serverUrl = await getServerUrl();\r\n        const response = await fetch(`${serverUrl}${url}?apiKey=${token}`, {\r\n            ...options,\r\n            headers\r\n        });\r\n\r\n        if (!response.ok) {\r\n            const errorData = await response.json();\r\n            throw new Error(errorData.message || 'API request failed');\r\n        }\r\n        const resp = await response.json();\r\n        return resp.data;\r\n    } catch (error) {\r\n        console.error(\"API request error:\", error);\r\n        throw error;\r\n    }\r\n}\r\n\r\n\r\nexport const apiService = {\r\n    getUserAccounts: async (authToken: AuthToken): Promise<UserInterface> => {\r\n        return fetchWithAuth({\r\n            url: '/user/detail',\r\n            options: { method: 'GET' },\r\n            token: authToken.token\r\n        });\r\n    },\r\n    saveJobApplication: async (\r\n        jobDetails: Application,\r\n        authToken: AuthToken\r\n    ): Promise<{ success: boolean, message: string }> => {\r\n        return fetchWithAuth({\r\n            url: '/application',\r\n            options: {\r\n                method: 'POST',\r\n                body: JSON.stringify({ data: {...jobDetails, stack: jobDetails.techStacks.join(', ')} }),\r\n            },\r\n            token: authToken.token\r\n        });\r\n    },\r\n    getLastJobDescription: async (authToken: AuthToken) => {\r\n        return fetchWithAuth({\r\n            url: '/application/getlastjob-description',\r\n            options: { method: 'GET' },\r\n            token: authToken.token\r\n        });\r\n    },\r\n\r\n    // ToDo implement verify token on backend\r\n    verifyToken: async (token: string): Promise<{ valid: boolean }> => {\r\n        try {\r\n            // Get dynamic server URL from storage\r\n            const serverUrl = await getServerUrl();\r\n            const response = await fetch(`${serverUrl}/verify-token`, {\r\n                method: 'POST',\r\n                headers: { 'Content-Type': 'application/json' },\r\n                body: JSON.stringify({ token }),\r\n            });\r\n            const resp = await response.json();\r\n            return resp.data;\r\n        } catch (error) {\r\n            console.error('Token verification error:', error);\r\n            return { valid: false };\r\n        }\r\n    }\r\n}", "import { apiService } from \"../services/api\";\r\nimport { Application } from \"../types\";\r\nimport { request } from \"../utils/aiJobParser\";\r\nimport { getAuthToken, getSelectedAccount, getAiServerUrl } from \"../utils/storage\";\r\nimport { AI_CONFIG, MESSAGE_TYPES } from \"../constants\";\r\n\r\nchrome.runtime.onMessage.addListener((message, sender, sendResponse) => {\r\n    if (message.type === MESSAGE_TYPES.SAVE_JOB) {\r\n        saveJob(message.payload)\r\n            .then(result => sendResponse(result))\r\n            .catch(error => {\r\n                console.error('Error saving Job: ', error);\r\n                sendResponse({ success: false, message: error.message });\r\n            });\r\n\r\n        return true;\r\n    }\r\n\r\n    if (message.type === MESSAGE_TYPES.CHECK_CONTENT_LOADED) {\r\n        chrome.scripting.executeScript(\r\n            {\r\n                target: { tabId: sender.tab?.id || 0 },\r\n                func: () => (window as any).contentScriptLoaded || false,\r\n            },\r\n            (results) => {\r\n                if (chrome.runtime.lastError) {\r\n                    console.error('Error checking content script:', chrome.runtime.lastError);\r\n                    sendResponse(false);\r\n                } else {\r\n                    sendResponse(results?.[0]?.result || false);\r\n                }\r\n            }\r\n        );\r\n        return true;\r\n    }\r\n\r\n    if (message.type === MESSAGE_TYPES.AI_JOB_PARSE) {\r\n        handleAIJobParse(message.prompt, message.isQuestion)\r\n            .then(result => sendResponse(result))\r\n            .catch(error => {\r\n                console.error('Error parsing job with AI:', error);\r\n                sendResponse({ success: false, error: error.message });\r\n            });\r\n\r\n        return true;\r\n    }\r\n\r\n    if (message.type === MESSAGE_TYPES.HOTKEY_UPDATED) {\r\n        // Broadcast hotkey update to all tabs\r\n        broadcastHotkeyUpdate(message.hotkey);\r\n        return true;\r\n    }\r\n});\r\n\r\nconst broadcastHotkeyUpdate = async (hotkey: string) => {\r\n    try {\r\n        const tabs = await chrome.tabs.query({});\r\n        for (const tab of tabs) {\r\n            if (tab.id && tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {\r\n                chrome.tabs.sendMessage(tab.id, {\r\n                    type: MESSAGE_TYPES.HOTKEY_UPDATED,\r\n                    hotkey: hotkey\r\n                }).catch(() => {\r\n                    // Ignore errors for tabs that don't have content script loaded\r\n                });\r\n            }\r\n        }\r\n    } catch (error) {\r\n        console.error('Error broadcasting hotkey update:', error);\r\n    }\r\n};\r\n\r\nconst handleAIJobParse = async (prompt: string, isQuestion = false): Promise<any> => {\r\n    try {\r\n        const roleContent = isQuestion\r\n            ? \"You are a helpful job Apply assistant answering questions of jobs with my resume\"\r\n            : \"You are a helpful JSON parser assistant parseing job details from text as JSON object.\";\r\n\r\n        // Get dynamic AI server URL from storage\r\n        const aiServerUrl = await getAiServerUrl();\r\n        const response = await fetch(`${aiServerUrl}/api/chat`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n                'Access-Control-Allow-Origin': '*'\r\n            },\r\n            body: JSON.stringify({\r\n                model: AI_CONFIG.model,\r\n                messages: [\r\n                    { role: \"system\", content: roleContent },\r\n                    { role: \"user\", content: prompt }\r\n                ],\r\n                temperature: AI_CONFIG.temperature,\r\n                stream: false\r\n            })\r\n        });\r\n\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        // Read the response as text first to handle potential streaming format\r\n        const responseText = await response.text();\r\n        console.log(\"Raw response:\", responseText);\r\n\r\n        let completion;\r\n        try {\r\n            completion = JSON.parse(responseText);\r\n        } catch (parseError) {\r\n            // If it's a streaming response, try to parse the last line\r\n            const lines = responseText.trim().split('\\n');\r\n            const lastLine = lines[lines.length - 1];\r\n            completion = JSON.parse(lastLine);\r\n        }\r\n\r\n        const message = completion.message;\r\n\r\n        if (!message) {\r\n            throw new Error(\"No message in completion\");\r\n        }\r\n\r\n        if (isQuestion) {\r\n            return message.content ?? \"\";\r\n        }\r\n\r\n        let text = message.content.trim();\r\n        if (text.startsWith(\"```\")) {\r\n            text = text.replace(/^```[a-z]*\\n/, \"\").replace(/```$/, \"\");\r\n        }\r\n\r\n        try {\r\n            const obj = JSON.parse(text);\r\n            return obj;\r\n        } catch (err) {\r\n            console.error(\"Failed to parse JSON:\", err, text);\r\n            throw err;\r\n        }\r\n    } catch (error: any) {\r\n        console.error(\"AI Job Parse Error:\", error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nconst saveJob = async (jobDetails: Application) => {\r\n    try {\r\n        const authToken = await getAuthToken();\r\n        const selectedAccount = await getSelectedAccount();\r\n\r\n        if (!authToken) {\r\n            return { success: false, message: 'Authentication token not found' };\r\n        }\r\n\r\n        if (!selectedAccount) {\r\n            return { success: false, message: 'No account selected' };\r\n        }\r\n\r\n        await apiService.saveJobApplication(\r\n            { ...jobDetails, accountId: selectedAccount.email },\r\n            authToken\r\n        );\r\n\r\n        return { success: true, message: 'Job saved successfully' };\r\n    } catch (error) {\r\n        console.error('Error saving job:', error);\r\n        return {\r\n            success: false,\r\n            message: error instanceof Error ? error.message : 'Unknown error occurred'\r\n        };\r\n    }\r\n};\r\n\r\nchrome.runtime.onInstalled.addListener(() => {\r\n    console.log('Job Tracker Extension installed');\r\n    chrome.contextMenus.create({\r\n        id: \"runTrack\",\r\n        title: \"Pick up the detail from the page\",\r\n        contexts: [\"all\"]\r\n    });\r\n\r\n    chrome.contextMenus.create({\r\n        id: \"getAnswerForSelectedText\",\r\n        title: \"Process selected Text\",\r\n        contexts: [\"selection\"]\r\n    });\r\n});\r\n\r\nchrome.contextMenus.onClicked.addListener(async (info, tab) => {\r\n    if (!tab || !tab.id) return;\r\n    if (info.menuItemId === \"runTrack\") {\r\n        chrome.scripting.executeScript({\r\n            target: { tabId: tab.id },\r\n            func: () => {\r\n                if ((window as any).runTracker) {\r\n                    (window as any).runTracker();\r\n                }\r\n            }\r\n        });\r\n    }\r\n    if (info.menuItemId === \"getAnswerForSelectedText\" && info.selectionText) {\r\n        await processAndCopyWithFeedback(info.selectionText, tab.id);\r\n    }\r\n});\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nasync function processAndCopy(selectedText: string): Promise<string | undefined> {\r\n    // Customize this logic as needed\r\n    const authToken = await getAuthToken();\r\n    const selectedAccount = await getSelectedAccount();\r\n    if (!authToken) return;\r\n    if (!selectedAccount) return;\r\n\r\n    const jobDetail = await apiService.getLastJobDescription(authToken);\r\n    console.log(`jobDetail: ${JSON.stringify(jobDetail)}`);\r\n    const prompt = `\r\n        You are a helpful job Apply assistant answering questions of jobs with my resume.\r\n        Your job is to answer job-related questions as if you’re me, using my resume and the job description. \r\n        The answers should sound confident, professional, and human — not robotic or generic. \r\n        Be concise and specific. Only mention experience or skills if they’re relevant to both the question and the job description.\r\n        And never include your voice like \"here is the answer for you\" or \"Please let me know if you have another question\" etc.\r\n        Only return result as my answer which I can directly use without removing such a thing.\r\n        Here is the job description: \r\n        // Job description start\r\n        ${jobDetail.jobDescription}\r\n        // Job description ends\r\n        ${selectedAccount.resume && `Here is my resume\r\n        // Resume go here\r\n        ${selectedAccount.resume}\r\n        // Resume ends`}\r\n        Now answer the question as me: ${selectedText}\r\n    `;\r\n    const result = await request(prompt, true);\r\n    console.log(result);\r\n    // Copy to clipboard\r\n    return result;\r\n}\r\n\r\nasync function processAndCopyWithFeedback(selectedText: string, tabId: number): Promise<void> {\r\n    const sendToast = (message: string, toastType: 'info' | 'success' | 'error' | 'loading' = 'info', duration: number = 3000) => {\r\n        chrome.tabs.sendMessage(tabId, {\r\n            type: MESSAGE_TYPES.SHOW_TOAST,\r\n            message,\r\n            toastType: toastType,\r\n            duration\r\n        }).catch(() => {\r\n            // Ignore errors if content script is not loaded\r\n        });\r\n    };\r\n\r\n    try {\r\n        // Show loading toast\r\n        sendToast('Generating AI response for selected text...', 'loading', 0);\r\n\r\n        const authToken = await getAuthToken();\r\n        const selectedAccount = await getSelectedAccount();\r\n\r\n        if (!authToken) {\r\n            sendToast('Authentication token not found. Please check your settings.', 'error');\r\n            return;\r\n        }\r\n\r\n        if (!selectedAccount) {\r\n            sendToast('No account selected. Please check your settings.', 'error');\r\n            return;\r\n        }\r\n\r\n        // Get job description\r\n        const jobDetail = await apiService.getLastJobDescription(authToken);\r\n\r\n        if (!jobDetail || !jobDetail.jobDescription) {\r\n            sendToast('No job description found. Please save a job first.', 'error');\r\n            return;\r\n        }\r\n\r\n        const prompt = `\r\n            You are a helpful job Apply assistant answering questions of jobs with my resume.\r\n            Your job is to answer job-related questions as if you're me, using my resume and the job description.\r\n            The answers should sound confident, professional, and human — not robotic or generic.\r\n            Be concise and specific. Only mention experience or skills if they're relevant to both the question and the job description.\r\n            And never include your voice like \"here is the answer for you\" or \"Please let me know if you have another question\" etc.\r\n            Only return result as my answer which I can directly use without removing such a thing.\r\n            Here is the job description:\r\n            // Job description start\r\n            ${jobDetail.jobDescription}\r\n            // Job description ends\r\n            ${selectedAccount.resume && `Here is my resume\r\n            // Resume go here\r\n            ${selectedAccount.resume}\r\n            // Resume ends`}\r\n            Now answer the question as me: ${selectedText}\r\n        `;\r\n\r\n        const result = await handleAIJobParse(prompt, true);\r\n\r\n        if (!result) {\r\n            sendToast('Failed to generate AI response. Please try again.', 'error');\r\n            return;\r\n        }\r\n\r\n        // Copy to clipboard\r\n        await chrome.scripting.executeScript({\r\n            target: { tabId },\r\n            func: (text) => {\r\n                return navigator.clipboard.writeText(text || \"\").then(() => {\r\n                    return true;\r\n                }).catch(err => {\r\n                    console.error(\"Clipboard write failed:\", err);\r\n                    return false;\r\n                });\r\n            },\r\n            args: [result]\r\n        });\r\n\r\n        sendToast('AI response generated and copied to clipboard!', 'success');\r\n\r\n    } catch (error) {\r\n        console.error('Error in processAndCopyWithFeedback:', error);\r\n        sendToast('Failed to process request. Please try again.', 'error');\r\n    }\r\n}\r\n\r\nconsole.log('Job Tracker Background Script Loaded');"], "names": ["STORAGE_KEYS", "DEFAULTS", "MESSAGE_TYPES", "AI_CONFIG", "AppError", "Error", "constructor", "message", "code", "statusCode", "super", "this", "name", "getStorageItem", "async", "key", "defaultValue", "_result$key", "result", "chrome", "storage", "local", "get", "operation", "runtime", "lastError", "console", "error", "handleChromeError", "logError", "context", "additionalData", "group", "stack", "groupEnd", "getAuthToken", "getSelectedAccount", "getServerUrl", "fetchWithAuth", "url", "options", "token", "props", "headers", "serverUrl", "response", "fetch", "ok", "errorData", "json", "data", "apiService", "jobDetails", "authToken", "method", "body", "JSON", "stringify", "techStacks", "join", "onMessage", "addListener", "sender", "sendResponse", "type", "saveJob", "payload", "then", "catch", "success", "scripting", "executeScript", "target", "tabId", "_sender$tab", "tab", "id", "func", "window", "contentScriptLoaded", "results", "_results$", "handleAIJobParse", "prompt", "isQuestion", "broadcastHotkeyUpdate", "hotkey", "tabs", "query", "startsWith", "sendMessage", "arguments", "length", "undefined", "<PERSON><PERSON>ontent", "aiServerUrl", "getAiServerUrl", "model", "messages", "role", "content", "temperature", "stream", "status", "responseText", "text", "completion", "log", "parse", "parseError", "lines", "trim", "split", "lastLine", "_message$content", "replace", "err", "selectedAccount", "accountId", "email", "onInstalled", "contextMenus", "create", "title", "contexts", "onClicked", "info", "menuItemId", "runTracker", "selectionText", "selectedText", "sendToast", "toastType", "duration", "jobDetail", "jobDescription", "resume", "navigator", "clipboard", "writeText", "args", "processAndCopyWithFeedback"], "sourceRoot": ""}