// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validates if a string is a valid URL
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Validates if a string is a valid email
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates if a token has the correct format
 */
export const isValidToken = (token: string): boolean => {
  return token.length > 10 && token.trim() === token;
};

/**
 * Validates if a hotkey combination is valid
 */
export const isValidHotkey = (hotkey: string): boolean => {
  const validModifiers = ['Control', 'Alt', 'Shift'];
  const parts = hotkey.split('+');
  
  if (parts.length < 2) return false;
  
  const modifiers = parts.slice(0, -1);
  const key = parts[parts.length - 1];
  
  // Check if all modifiers are valid
  const hasValidModifiers = modifiers.every(mod => validModifiers.includes(mod));
  
  // Check if key is not empty and not a modifier
  const hasValidKey = Boolean(key && !validModifiers.includes(key));

  return hasValidModifiers && hasValidKey;
};

/**
 * Validates job application data
 */
export const validateJobApplication = (job: any): string[] => {
  const errors: string[] = [];
  
  if (!job.companyName?.trim()) {
    errors.push('Company name is required');
  }
  
  if (!job.jobRole?.trim()) {
    errors.push('Job role is required');
  }
  
  if (!job.jobUrl?.trim()) {
    errors.push('Job URL is required');
  } else if (!isValidUrl(job.jobUrl)) {
    errors.push('Job URL must be a valid URL');
  }
  
  if (!Array.isArray(job.techStacks)) {
    errors.push('Tech stacks must be an array');
  }
  
  return errors;
};

/**
 * Validates account data
 */
export const validateAccount = (account: any): string[] => {
  const errors: string[] = [];
  
  if (!account.email?.trim()) {
    errors.push('Email is required');
  } else if (!isValidEmail(account.email)) {
    errors.push('Email must be a valid email address');
  }
  
  // if (!account.nationality?.trim()) {
  //   errors.push('Nationality is required');
  // }
  
  // if (!account.address?.trim()) {
  //   errors.push('Address is required');
  // }
  
  // if (!account.university?.trim()) {
  //   errors.push('University is required');
  // }
  
  return errors;
};

/**
 * Sanitizes HTML content to prevent XSS
 */
export const sanitizeHtml = (html: string): string => {
  const div = document.createElement('div');
  div.textContent = html;
  return div.innerHTML;
};

/**
 * Validates and sanitizes user input
 */
export const sanitizeInput = (input: string, maxLength = 1000): string => {
  return input.trim().slice(0, maxLength);
};
