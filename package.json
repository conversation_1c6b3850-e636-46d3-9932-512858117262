{"name": "job-tracker", "version": "0.1.1", "private": true, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/themes": "^3.2.1", "@tanstack/react-query": "^5.69.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@ts-stack/markdown": "^1.5.0", "@types/jest": "^27.5.2", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "groq-sdk": "^0.17.0", "input-otp": "^1.4.2", "lucide-react": "^0.484.0", "next-themes": "^0.4.6", "openai": "^4.96.0", "pdfjs-dist": "^5.1.91", "react": "^18.2.0", "react-day-picker": "^9.6.3", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.4.0", "react-scripts": "5.0.1", "recharts": "^2.15.1", "sonner": "^2.0.2", "styled-components": "^6.1.8", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "typescript": "^4.9.5", "vaul": "^1.1.2", "web-vitals": "^2.1.4", "zod": "^3.24.2"}, "scripts": {"start": "react-scripts start", "build": "craco build", "build:dev": "cross-env NODE_ENV=development craco build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@tailwindcss/typography": "^0.5.16", "@types/chrome": "^0.0.260", "@types/node": "^22.13.14", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "cross-env": "^7.0.3"}}