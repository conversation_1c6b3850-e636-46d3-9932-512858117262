// No imports needed - using chrome.runtime.sendMessage for communication

export const buildPrompt = (text: string) => `
You are a JSON-only assistant. Do not include any explanation or extra text.

Extract job information from the following job posting. Output must be a valid JSON object only.

Fields to extract:
- jobTitle
- companyName
- companyNationality
- companySite
- remoteAvailability (Remote Only, Hybrid, Remote Possible, On-site, Not Specified)
- techStacks (Array of strings)
- jobDescription (structured full description with bullets, some symbols to easy to see overview in Markdown and max limit 3000 letters)

Return in this exact JSON structure:

{
  "jobTitle": "",
  "companyName": "",
  "companyNationality": "",
  "companySite": "",
  "remoteAvailability": "",
  "techStacks": [],
  "jobDescription": ""
}

--- BEGIN JOB INFO(Url or content) ---
${text.slice(0, 6000)}
--- END JOB INFO ---
    - note when picking infos
        . jobTitle should be position title.
        . try to get companyNationality and companySite(link to the company's website) if applicable but optional
        . jobDescription should include all the important informations like key requirement, company introduction and salary, etc in structured way
        . companyName should be company who are looking for candidate, not job site's name you can find it from job description
`;

export const request = async (prompt: string, isQuestion = false): Promise<any | null> => {
    try {
        // Send message to background script to handle the AI request
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(
                {
                    type: "AI_JOB_PARSE",
                    prompt: prompt,
                    isQuestion: isQuestion
                },
                (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }

                    if (response && response.success === false) {
                        reject(new Error(response.error || 'AI parsing failed'));
                        return;
                    }

                    resolve(response);
                }
            );
        });
    } catch (error: any) {
        console.error("Error in AI job parsing request:", error);
        throw error;
    }
};