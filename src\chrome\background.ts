import { apiService } from "../services/api";
import { Application } from "../types";
import { request } from "../utils/aiJobParser";
import { getAuthToken, getSelectedAccount, getAiServerUrl } from "../utils/storage";
import { AI_CONFIG, MESSAGE_TYPES } from "../constants";

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === MESSAGE_TYPES.SAVE_JOB) {
        saveJob(message.payload)
            .then(result => sendResponse(result))
            .catch(error => {
                console.error('Error saving Job: ', error);
                sendResponse({ success: false, message: error.message });
            });

        return true;
    }

    if (message.type === MESSAGE_TYPES.CHECK_CONTENT_LOADED) {
        chrome.scripting.executeScript(
            {
                target: { tabId: sender.tab?.id || 0 },
                func: () => (window as any).contentScriptLoaded || false,
            },
            (results) => {
                if (chrome.runtime.lastError) {
                    console.error('Error checking content script:', chrome.runtime.lastError);
                    sendResponse(false);
                } else {
                    sendResponse(results?.[0]?.result || false);
                }
            }
        );
        return true;
    }

    if (message.type === MESSAGE_TYPES.AI_JOB_PARSE) {
        handleAIJobParse(message.prompt, message.isQuestion)
            .then(result => sendResponse(result))
            .catch(error => {
                console.error('Error parsing job with AI:', error);
                sendResponse({ success: false, error: error.message });
            });

        return true;
    }

    if (message.type === MESSAGE_TYPES.HOTKEY_UPDATED) {
        // Broadcast hotkey update to all tabs
        broadcastHotkeyUpdate(message.hotkey);
        return true;
    }
});

const broadcastHotkeyUpdate = async (hotkey: string) => {
    try {
        const tabs = await chrome.tabs.query({});
        for (const tab of tabs) {
            if (tab.id && tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                chrome.tabs.sendMessage(tab.id, {
                    type: MESSAGE_TYPES.HOTKEY_UPDATED,
                    hotkey: hotkey
                }).catch(() => {
                    // Ignore errors for tabs that don't have content script loaded
                });
            }
        }
    } catch (error) {
        console.error('Error broadcasting hotkey update:', error);
    }
};

const handleAIJobParse = async (prompt: string, isQuestion = false): Promise<any> => {
    try {
        const roleContent = isQuestion
            ? "You are a helpful job Apply assistant answering questions of jobs with my resume"
            : "You are a helpful JSON parser assistant parseing job details from text as JSON object.";

        // Get dynamic AI server URL from storage
        const aiServerUrl = await getAiServerUrl();
        const response = await fetch(`${aiServerUrl}/api/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
                model: AI_CONFIG.model,
                messages: [
                    { role: "system", content: roleContent },
                    { role: "user", content: prompt }
                ],
                temperature: AI_CONFIG.temperature,
                stream: false
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Read the response as text first to handle potential streaming format
        const responseText = await response.text();
        console.log("Raw response:", responseText);

        let completion;
        try {
            completion = JSON.parse(responseText);
        } catch (parseError) {
            // If it's a streaming response, try to parse the last line
            const lines = responseText.trim().split('\n');
            const lastLine = lines[lines.length - 1];
            completion = JSON.parse(lastLine);
        }

        const message = completion.message;

        if (!message) {
            throw new Error("No message in completion");
        }

        if (isQuestion) {
            return message.content ?? "";
        }

        let text = message.content.trim();
        if (text.startsWith("```")) {
            text = text.replace(/^```[a-z]*\n/, "").replace(/```$/, "");
        }

        try {
            const obj = JSON.parse(text);
            return obj;
        } catch (err) {
            console.error("Failed to parse JSON:", err, text);
            throw err;
        }
    } catch (error: any) {
        console.error("AI Job Parse Error:", error);
        throw error;
    }
};

const saveJob = async (jobDetails: Application) => {
    try {
        const authToken = await getAuthToken();
        const selectedAccount = await getSelectedAccount();

        if (!authToken) {
            return { success: false, message: 'Authentication token not found' };
        }

        if (!selectedAccount) {
            return { success: false, message: 'No account selected' };
        }

        await apiService.saveJobApplication(
            { ...jobDetails, accountId: selectedAccount.email },
            authToken
        );

        return { success: true, message: 'Job saved successfully' };
    } catch (error) {
        console.error('Error saving job:', error);
        return {
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
};

chrome.runtime.onInstalled.addListener(() => {
    console.log('Job Tracker Extension installed');
    chrome.contextMenus.create({
        id: "runTrack",
        title: "Pick up the detail from the page",
        contexts: ["all"]
    });

    chrome.contextMenus.create({
        id: "getAnswerForSelectedText",
        title: "Process selected Text",
        contexts: ["selection"]
    });
});

chrome.contextMenus.onClicked.addListener(async (info, tab) => {
    if (!tab || !tab.id) return;
    if (info.menuItemId === "runTrack") {
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: () => {
                if ((window as any).runTracker) {
                    (window as any).runTracker();
                }
            }
        });
    }
    if (info.menuItemId === "getAnswerForSelectedText" && info.selectionText) {
        await processAndCopyWithFeedback(info.selectionText, tab.id);
    }
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function processAndCopy(selectedText: string): Promise<string | undefined> {
    // Customize this logic as needed
    const authToken = await getAuthToken();
    const selectedAccount = await getSelectedAccount();
    if (!authToken) return;
    if (!selectedAccount) return;

    const jobDetail = await apiService.getLastJobDescription(authToken);
    console.log(`jobDetail: ${JSON.stringify(jobDetail)}`);
    const prompt = `
        You are a helpful job Apply assistant answering questions of jobs with my resume.
        Your job is to answer job-related questions as if you’re me, using my resume and the job description. 
        The answers should sound confident, professional, and human — not robotic or generic. 
        Be concise and specific. Only mention experience or skills if they’re relevant to both the question and the job description.
        And never include your voice like "here is the answer for you" or "Please let me know if you have another question" etc.
        Only return result as my answer which I can directly use without removing such a thing.
        Here is the job description: 
        // Job description start
        ${jobDetail.jobDescription}
        // Job description ends
        ${selectedAccount.resume && `Here is my resume
        // Resume go here
        ${selectedAccount.resume}
        // Resume ends`}
        Now answer the question as me: ${selectedText}
    `;
    const result = await request(prompt, true);
    console.log(result);
    // Copy to clipboard
    return result;
}

async function processAndCopyWithFeedback(selectedText: string, tabId: number): Promise<void> {
    const sendToast = (message: string, toastType: 'info' | 'success' | 'error' | 'loading' = 'info', duration: number = 3000) => {
        chrome.tabs.sendMessage(tabId, {
            type: MESSAGE_TYPES.SHOW_TOAST,
            message,
            toastType: toastType,
            duration
        }).catch(() => {
            // Ignore errors if content script is not loaded
        });
    };

    try {
        // Show loading toast
        sendToast('Generating AI response for selected text...', 'loading', 0);

        const authToken = await getAuthToken();
        const selectedAccount = await getSelectedAccount();

        if (!authToken) {
            sendToast('Authentication token not found. Please check your settings.', 'error');
            return;
        }

        if (!selectedAccount) {
            sendToast('No account selected. Please check your settings.', 'error');
            return;
        }

        // Get job description
        const jobDetail = await apiService.getLastJobDescription(authToken);

        if (!jobDetail || !jobDetail.jobDescription) {
            sendToast('No job description found. Please save a job first.', 'error');
            return;
        }

        const prompt = `
            You are a helpful job Apply assistant answering questions of jobs with my resume.
            Your job is to answer job-related questions as if you're me, using my resume and the job description.
            The answers should sound confident, professional, and human — not robotic or generic.
            Be concise and specific. Only mention experience or skills if they're relevant to both the question and the job description.
            And never include your voice like "here is the answer for you" or "Please let me know if you have another question" etc.
            Only return result as my answer which I can directly use without removing such a thing.
            Here is the job description:
            // Job description start
            ${jobDetail.jobDescription}
            // Job description ends
            ${selectedAccount.resume && `Here is my resume
            // Resume go here
            ${selectedAccount.resume}
            // Resume ends`}
            Now answer the question as me: ${selectedText}
        `;

        const result = await handleAIJobParse(prompt, true);

        if (!result) {
            sendToast('Failed to generate AI response. Please try again.', 'error');
            return;
        }

        // Copy to clipboard
        await chrome.scripting.executeScript({
            target: { tabId },
            func: (text) => {
                return navigator.clipboard.writeText(text || "").then(() => {
                    return true;
                }).catch(err => {
                    console.error("Clipboard write failed:", err);
                    return false;
                });
            },
            args: [result]
        });

        sendToast('AI response generated and copied to clipboard!', 'success');

    } catch (error) {
        console.error('Error in processAndCopyWithFeedback:', error);
        sendToast('Failed to process request. Please try again.', 'error');
    }
}

console.log('Job Tracker Background Script Loaded');