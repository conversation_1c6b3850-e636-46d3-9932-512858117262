import { useEffect, useState, type FC } from "react";
import { Account, UserInterface } from "../types";
import { getAuthToken, saveSelectedAccount } from "../utils/storage";
import { apiService } from "../services/api";
import fetchAndparsePdf from "../utils/pdfParser";
import { <PERSON>ton, Card, CardHeader, CardContent, CardFooter, Loading, Skeleton } from "./ui";

interface AccountSelectionProps {
    onAccountSelected: () => void;
}

const AccountSelection: FC<AccountSelectionProps> = ({ onAccountSelected }) => {
    const [account, setAccount] = useState<UserInterface>();
    const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
        const fetchAccounts = async () => {
            try {
                setIsLoading(true);
                const authToken = await getAuthToken();

                if (!authToken) {
                    setError('Authentication token not found. Please restart the setup process.');
                    return;
                }

                const accountInfo = await apiService.getUserAccounts(authToken);
                setAccount(accountInfo);

                if (accountInfo.accounts.length > 0) {
                    setSelectedAccount(accountInfo.accounts[0]);
                }
            } catch (error) {
                setError('Failed to load accounts. Please try again.');
                console.error('Error loading accounts:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchAccounts();
    }, []);

    const handleSelectAccount = (acc: Account) => {
        setSelectedAccount(acc);
    };

    const handleConfirmSelection = async () => {
        if (!selectedAccount) {
            setError("Please select an account");
            return;
        }
        setIsSaving(true);
        try {
            const selected: Account | undefined = account?.accounts.find(acc => acc.email === selectedAccount.email);

            if (!selected) {
                setError('Selected Account not found');
                return;
            }
            if (selected.cv) {
                const resume = await fetchAndparsePdf(selected.cv);
                selected.resume = resume;
            }

            await saveSelectedAccount(selected);
            onAccountSelected();
        } catch (error) {
            setError('Failed to save Selected account. Please try again.');
            console.error('Error saving selected account: ', error);
        } finally {
            setIsSaving(false);
        }
    };


    return (
        <div className="container animate-fade-in">
            <Card variant="elevated" padding="lg">
                <CardHeader>
                    <div className="text-center w-full">
                        <div className="bg-blue-100 p-4 rounded-full inline-flex items-center justify-center mb-4">
                            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Select Account</h2>
                        <p className="text-gray-600">Choose an account to use with the job tracker</p>
                    </div>
                </CardHeader>

                <CardContent>
                    {isLoading ? (
                        <div className="space-y-4">
                            <Skeleton height={60} />
                            <Skeleton height={60} />
                            <Skeleton height={60} />
                            <Loading text="Loading accounts..." />
                        </div>
                    ) : account && account.accounts.length > 0 ? (
                        <div className="space-y-3">
                            {account.accounts.map(acc => (
                                <Card
                                    key={acc.email}
                                    variant={selectedAccount?.email === acc.email ? "elevated" : "outlined"}
                                    padding="md"
                                    className={`cursor-pointer transition-all hover:shadow-md ${selectedAccount?.email === acc.email
                                        ? 'ring-2 ring-blue-500 border-blue-500'
                                        : 'hover:border-gray-300'
                                        }`}
                                    onClick={() => handleSelectAccount(acc)}
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                                <span className="text-blue-600 font-medium text-lg">
                                                    {acc.email.charAt(0).toUpperCase()}
                                                </span>
                                            </div>
                                            <div>
                                                <h3 className="font-medium text-gray-900">{acc.email}</h3>
                                                <p className="text-sm text-gray-500">{acc.nationality} • {acc.university}</p>
                                            </div>
                                        </div>
                                        {selectedAccount?.email === acc.email && (
                                            <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                        )}
                                    </div>
                                </Card>
                            ))}

                            {error && (
                                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                                    <p className="text-red-600 text-sm">{error}</p>
                                </div>
                            )}
                        </div>) : (
                        <div className="text-center py-8">
                            <div className="bg-gray-100 p-4 rounded-full inline-flex items-center justify-center mb-4">
                                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Accounts Found</h3>
                            <p className="text-gray-600">Please check your authentication token and try again.</p>
                            {error && (
                                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                                    <p className="text-red-600 text-sm">{error}</p>
                                </div>
                            )}
                        </div>
                    )}
                </CardContent>

                <CardFooter>
                    <Button
                        onClick={handleConfirmSelection}
                        loading={isSaving}
                        disabled={!selectedAccount}
                        fullWidth
                        size="lg"
                    >
                        {isSaving ? 'Saving...' : 'Confirm Selection'}
                    </Button>
                </CardFooter>
            </Card>
        </div>
    );
};

export default AccountSelection;