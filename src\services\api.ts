import { Application, AuthToken, UserInterface } from "../types";
import { API_CONFIG } from "../constants";
import { getServerUrl } from "../utils/storage";

interface FetchWithAuthProps {
    url: string;
    options: RequestInit,
    token: string
}
const fetchWithAuth = async (props: FetchWithAuthProps) => {
    const { url, options, token } = props;
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };

    try {
        // Get dynamic server URL from storage
        const serverUrl = await getServerUrl();
        const response = await fetch(`${serverUrl}${url}?apiKey=${token}`, {
            ...options,
            headers
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'API request failed');
        }
        const resp = await response.json();
        return resp.data;
    } catch (error) {
        console.error("API request error:", error);
        throw error;
    }
}


export const apiService = {
    getUserAccounts: async (authToken: AuthToken): Promise<UserInterface> => {
        return fetchWithAuth({
            url: '/user/detail',
            options: { method: 'GET' },
            token: authToken.token
        });
    },
    saveJobApplication: async (
        jobDetails: Application,
        authToken: AuthToken
    ): Promise<{ success: boolean, message: string }> => {
        return fetchWithAuth({
            url: '/application',
            options: {
                method: 'POST',
                body: JSON.stringify({ data: {...jobDetails, stack: jobDetails.techStacks.join(', ')} }),
            },
            token: authToken.token
        });
    },
    getLastJobDescription: async (authToken: AuthToken) => {
        return fetchWithAuth({
            url: '/application/getlastjob-description',
            options: { method: 'GET' },
            token: authToken.token
        });
    },

    // ToDo implement verify token on backend
    verifyToken: async (token: string): Promise<{ valid: boolean }> => {
        try {
            // Get dynamic server URL from storage
            const serverUrl = await getServerUrl();
            const response = await fetch(`${serverUrl}/verify-token`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token }),
            });
            const resp = await response.json();
            return resp.data;
        } catch (error) {
            console.error('Token verification error:', error);
            return { valid: false };
        }
    }
}