import { Application } from "../types";

// Common tech stack keywords to look for
const TECH_KEYWORDS = [
    "javascript", "typescript", "react", "angular", "vue", "node", "express",
    "python", "django", "flask", "java", "spring", "rust", "go", "golang",
    "ruby", "rails", "php", "laravel", "c#", ".net", "aws", "azure", "gcp",
    "docker", "kubernetes", "sql", "nosql", "mongodb", "postgresql", "mysql",
    "graphql", "rest", "html", "css", "sass", "less", "tailwind", "bootstrap"
];

// Function to extract job title from the page
const extractJobTitle = (): string => {
    // Common job title selectors
    const selectors = [
        'h1.job-title',
        'h1.posting-title',
        'h1[data-testid="job-title"]',
        'h1.topcard__title',
        '.job-details-jobs-unified-top-card__job-title',
        '.jobs-unified-top-card__job-title',
        // LinkedIn
        '.top-card-layout__title',
        // Indeed
        '.jobsearch-JobInfoHeader-title',
        // Glassdoor
        'h1[class*="heading_Level1"]',
        'h1.text-3xl.font-semibold.text-primary',
        'h1.section-header.section-header--large.font-primary',
        'h1.mb-3.w-max.max-w-5xl.text-3xl.font-medium.text-gray-900',
        'h1.ashby-job-posting-heading',
        'div.job__title h1',
        'h2.jv-header',
        'div.posting-headline h2',
        'h2[data-automation-id="jobPostingHeader"]',
        '#header h1.app-title',
        'h1.jobsearch-JobInfoHeader-title span',
        'div.job-header div.container h1',
        'div#jobdetails h1'

    ];

    for (const selector of selectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent) {
            return element.textContent.trim();
        }
    }
    // Generic approach: look for h1 tags with common job title keywords
    const h1Elements = document.querySelectorAll('h1');
    for (const h1 of h1Elements) {
        const text = h1.textContent?.toLowerCase() || '';
        if (text.includes('software') ||
            text.includes('developer') ||
            text.includes('engineer') ||
            text.includes('position')) {
            return h1.textContent?.trim() || 'Unknown Job Title';
        }
    }

    return 'Unknown Job Title';
};

// Function to extract company name from the page
const extractCompanyName = (): string => {
    // Common company name selectors
    const selectors = [
        '.company-name',
        '.topcard__org-name-link',
        '.job-details-jobs-unified-top-card__company-name',
        // LinkedIn
        '.jobs-unified-top-card__company-name',
        '.topcard__org-name-link',
        // Indeed
        '.jobsearch-InlineCompanyRating > div:first-child',
        // Glassdoor
        '.employer-name',
        // General fallbacks
        'a[data-tracking-control-name="public_jobs_topcard-org-name"]',
        'a[data-tracking-control-name="public_jobs_topcard_org_name"]',
        'h4[class*="heading_Subhead__"]',
        'h2.text-lg.font-semibold.text-center.text-primary.mb-1.mt-2 a',
        'a.hiring_company',
        'a.font-medium.text-gray-700',
        'img[class*="_navLogoWordmarkImage"]',
        'h1.jv-logo a',
        'div#header span.company-name',
        'div[data-testid="jobsearch-CompanyInfoContainer"] div a',
        'div.job-board-listing-header div.container h1.brand-text a',
        'div#jobdetails a[data-cy="companyNameLink"]'
    ];

    for (const selector of selectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent) {
            if (
                window.location.href.includes('boards.greenhouse.io')
                && !window.location.href.includes('job-boards.greenhouse.io')
            ) {
                return element.textContent.trim().split('at')[1].trim();
            }
            if (element.textContent.toLowerCase().includes("careers"))
                return element.textContent.trim().split("careers")[0].trim();
            return element.textContent.trim();
        } if (element && element.tagName === 'IMG') {
            return (element as HTMLImageElement).alt.trim();
        }
    }

    if (window.location.href.includes("greenhouse.io")) {
        const title = document.title.split(' at ')[1];
        return title;
    } else if (window.location.href.includes("jobs.lever.co")) {
        return document.title.split(" - ")[0];
    } else if (window.location.href.includes("myworkdayjobs.com")) {
        return JSON.parse(document.querySelector('script[type="application/ld+json"]')?.textContent || '{}')?.hiringOrganization?.name;
    }

    return 'Unknown Company';
};

// Function to extract job description from the page
const extractJobDescription = (): string => {
    // Common job description selectors
    const selectors = [
        '.job-description',
        '.description__text',
        '.jobsearch-jobDescriptionText',
        '#js-greenhouse-job-description',
        '#job-details',
        // LinkedIn
        '.jobs-description-content',
        '.jobs-description__content',
        // Indeed
        '#jobDescriptionText',
        // Glassdoor
        '.jobDescriptionContent',
        // General fallbacks
        '[data-automation="jobDescriptionSection"]',
        'div[class*="description"]',
        'section[class*="description"]',
        'div[class*="JobDetails_jobDescription__"',
        'div.bg-primary.flex.flex-col.items-start.rounded-lg.p-4.border-gray-800',
        'div.job_content',
        'section.container.mx-auto.px-4.pb-16',
        'div[class*="_descriptionText_"]',
        'div.job__description.body',
        'div.jv-job-detail-description',
        'div.section.page-centered[data-qa="job-description"]',
        'div[data-automation-id="jobPostingDescription"]',
        'div#content',
        'div#jobDescriptionText',
        'div#job-description',
        'section.job-description',
        'section.company_description'
    ];

    for (const selector of selectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent?.trim()) {
            // return element.textContent.trim();
            return element.innerHTML.trim();
        }
    }

    return 'No job description found';
};

// Function to extract location information
const extractLocation = (): string => {
    // Common location selectors
    const selectors = [
        '.location',
        '.job-location',
        '.topcard__flavor--bullet',
        // LinkedIn
        '.jobs-unified-top-card__workplace-type',
        '.jobs-unified-top-card__bullet',
        // Indeed
        '.jobsearch-JobInfoHeader-subtitle .jobsearch-JobInfoHeader-companyLocation',
        // Glassdoor
        '.location'
    ];

    for (const selector of selectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent) {
            return element.textContent.trim();
        }
    }

    return 'Unknown Location';
};

// Function to determine remote availability
const extractRemoteAvailability = (description: string): string => {
    const lowerDesc = description.toLowerCase();

    if (lowerDesc.includes('remote only') ||
        lowerDesc.includes('fully remote') ||
        lowerDesc.includes('100% remote')) {
        return 'Remote Only';
    } else if (lowerDesc.includes('hybrid') ||
        lowerDesc.includes('flexible')) {
        return 'Hybrid';
    } else if (lowerDesc.includes('remote') ||
        lowerDesc.includes('work from home') ||
        lowerDesc.includes('wfh')) {
        return 'Remote Possible';
    } else if (lowerDesc.includes('onsite') ||
        lowerDesc.includes('on-site') ||
        lowerDesc.includes('in office')) {
        return 'On-site';
    }

    return 'Not Specified';
};

// Function to extract tech stacks from job description
const extractTechStacks = (description: string): string[] => {
    const lowerDesc = description.toLowerCase();
    return TECH_KEYWORDS.filter(tech => lowerDesc.includes(tech.toLowerCase()));
};

const getJobManually = (): Application => {
    const jobRole = extractJobTitle();
    const companyName = extractCompanyName();
    const jobDescription = extractJobDescription();
    const remoteAvailability = extractRemoteAvailability(jobDescription);
    const techStacks = extractTechStacks(jobDescription);

    return {
        jobRole,
        companyName,
        jobDescription,
        remoteAvailability,
        techStacks,
        jobUrl: window.location.href
    };
};

export default getJobManually;