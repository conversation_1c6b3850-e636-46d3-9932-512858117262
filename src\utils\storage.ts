// ============================================================================
// IMPROVED STORAGE UTILITIES
// ============================================================================

import { AuthToken, Account, ChromeStorage } from "../types";
import { STORAGE_KEYS, DEFAULTS } from "../constants";
import { handleChromeError, logError, AppError } from "./errorHandling";
import { validateAccount, isValidToken } from "./validation";

/**
 * Generic storage getter with error handling and type safety
 */
const getStorageItem = async <T>(key: string, defaultValue?: T): Promise<T | undefined> => {
    try {
        const result = await chrome.storage.local.get([key]);
        handleChromeError('storage.get');
        return result[key] ?? defaultValue;
    } catch (error) {
        logError(error as Error, `getStorageItem(${key})`);
        return defaultValue;
    }
};

/**
 * Generic storage setter with error handling and validation
 */
const setStorageItem = async <T>(key: string, value: T): Promise<void> => {
    try {
        await chrome.storage.local.set({ [key]: value });
        handleChromeError('storage.set');
        console.info(`✅ Storage item '${key}' saved successfully`);
    } catch (error) {
        logError(error as Error, `setStorageItem(${key})`, { value });
        throw new AppError(`Failed to save ${key} to storage`);
    }
};

/**
 * Get all storage data
 */
export const getAllStorageData = async (): Promise<ChromeStorage> => {
    try {
        const result = await chrome.storage.local.get(null);
        handleChromeError('storage.get');
        return result as ChromeStorage;
    } catch (error) {
        logError(error as Error, 'getAllStorageData');
        return {};
    }
};

/**
 * Clear all storage data
 */
export const clearAllStorage = async (): Promise<void> => {
    try {
        await chrome.storage.local.clear();
        handleChromeError('storage.clear');
        console.info('✅ All storage data cleared');
    } catch (error) {
        logError(error as Error, 'clearAllStorage');
        throw new AppError('Failed to clear storage');
    }
};

// ============================================================================
// AUTH TOKEN MANAGEMENT
// ============================================================================

export const saveAuthToken = async (token: string): Promise<void> => {
    if (!token?.trim()) {
        throw new AppError('Token cannot be empty');
    }
    
    if (!isValidToken(token)) {
        throw new AppError('Invalid token format');
    }
    
    const authToken: AuthToken = { token: token.trim() };
    await setStorageItem(STORAGE_KEYS.authToken, authToken);
};

export const getAuthToken = async (): Promise<AuthToken | null> => {
    const result = await getStorageItem<AuthToken>(STORAGE_KEYS.authToken);
    return result ?? null;
};

export const removeAuthToken = async (): Promise<void> => {
    try {
        await chrome.storage.local.remove([STORAGE_KEYS.authToken]);
        handleChromeError('storage.remove');
        console.info('✅ Auth token removed');
    } catch (error) {
        logError(error as Error, 'removeAuthToken');
        throw new AppError('Failed to remove auth token');
    }
};

// ============================================================================
// ACCOUNT MANAGEMENT
// ============================================================================

export const saveSelectedAccount = async (account: Account): Promise<void> => {
    const errors = validateAccount(account);
    if (errors.length > 0) {
        throw new AppError(`Invalid account data: ${errors.join(', ')}`);
    }
    
    await setStorageItem(STORAGE_KEYS.selectedAccount, account);
};

export const getSelectedAccount = async (): Promise<Account | null> => {
    const result = await getStorageItem<Account>(STORAGE_KEYS.selectedAccount);
    return result ?? null;
};

export const removeSelectedAccount = async (): Promise<void> => {
    try {
        await chrome.storage.local.remove([STORAGE_KEYS.selectedAccount]);
        handleChromeError('storage.remove');
        console.info('✅ Selected account removed');
    } catch (error) {
        logError(error as Error, 'removeSelectedAccount');
        throw new AppError('Failed to remove selected account');
    }
};

// ============================================================================
// HOTKEY MANAGEMENT
// ============================================================================

export const saveHotkey = async (hotkey: string): Promise<void> => {
    if (!hotkey?.trim()) {
        throw new AppError('Hotkey cannot be empty');
    }
    
    await setStorageItem(STORAGE_KEYS.hotkey, hotkey.trim());
};

export const getHotkey = async (): Promise<string> => {
    return await getStorageItem<string>(STORAGE_KEYS.hotkey, DEFAULTS.hotkey) || DEFAULTS.hotkey;
};

// ============================================================================
// THEME MANAGEMENT
// ============================================================================

export const saveTheme = async (theme: 'light' | 'dark'): Promise<void> => {
    await setStorageItem(STORAGE_KEYS.theme, theme);
};

export const getTheme = async (): Promise<'light' | 'dark'> => {
    return await getStorageItem<'light' | 'dark'>(STORAGE_KEYS.theme, DEFAULTS.theme) || DEFAULTS.theme;
};

// ============================================================================
// SERVER URL MANAGEMENT
// ============================================================================

export const saveServerUrl = async (url: string): Promise<void> => {
    if (!url?.trim()) {
        throw new AppError('Server URL cannot be empty');
    }

    // Basic URL validation
    try {
        new URL(url.trim());
    } catch {
        throw new AppError('Invalid server URL format');
    }

    await setStorageItem(STORAGE_KEYS.serverUrl, url.trim());
};

export const getServerUrl = async (): Promise<string> => {
    return await getStorageItem<string>(STORAGE_KEYS.serverUrl, DEFAULTS.serverUrl) || DEFAULTS.serverUrl;
};

export const saveAiServerUrl = async (url: string): Promise<void> => {
    if (!url?.trim()) {
        throw new AppError('AI Server URL cannot be empty');
    }

    // Basic URL validation
    try {
        new URL(url.trim());
    } catch {
        throw new AppError('Invalid AI server URL format');
    }

    await setStorageItem(STORAGE_KEYS.aiServerUrl, url.trim());
};

export const getAiServerUrl = async (): Promise<string> => {
    return await getStorageItem<string>(STORAGE_KEYS.aiServerUrl, DEFAULTS.aiServerUrl) || DEFAULTS.aiServerUrl;
};

// ============================================================================
// STORAGE EVENT LISTENERS
// ============================================================================

export const onStorageChanged = (
    callback: (changes: { [key: string]: chrome.storage.StorageChange }) => void
): void => {
    chrome.storage.onChanged.addListener((changes, areaName) => {
        if (areaName === 'local') {
            callback(changes);
        }
    });
};
