// ============================================================================
// CLASS NAME UTILITY
// ============================================================================

/**
 * Utility function to merge class names conditionally
 * Similar to clsx/classnames but lightweight
 * Supports strings, arrays, and conditional values
 */
export function cn(...classes: (string | string[] | undefined | null | false)[]): string {
  return classes
    .flat()
    .filter(Boolean)
    .join(' ')
    .trim();
}

/**
 * Utility to merge Tailwind classes with proper precedence
 * Handles conflicting classes by keeping the last one
 */
export function twMerge(...classes: string[]): string {
  const classMap = new Map<string, string>();
  
  classes
    .join(' ')
    .split(' ')
    .filter(Boolean)
    .forEach(cls => {
      // Extract the base class (e.g., 'bg' from 'bg-red-500')
      const baseClass = cls.split('-')[0];
      classMap.set(baseClass, cls);
    });
  
  return Array.from(classMap.values()).join(' ');
}
