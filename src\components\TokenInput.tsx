import { useState, type FC } from "react";
import { saveAuthToken, saveHotkey } from "../utils/storage";
import { apiService } from "../services/api";
import { Button, Input, Card, CardHeader, CardContent } from "./ui";
import { isValidToken } from "../utils/validation";

interface TokenInputProps {
    onTokenSaved: () => void;
}

const TokenInput: FC<TokenInputProps> = ({ onTokenSaved }) => {
    const [token, setToken] = useState('');
    const [isVerifying, setIsVerifying] = useState(false);
    const [error, setError] = useState('');

    const handleVerifyToken = async () => {
        // Client-side validation
        if (!token.trim()) {
            setError('Please enter a valid token');
            return;
        }

        if (!isValidToken(token)) {
            setError('Token format is invalid');
            return;
        }

        setIsVerifying(true);
        setError('');

        try {
            const result = await apiService.verifyToken(token);
            console.log(result);
            if (result.valid) {
                await saveAuthToken(token);
                await saveHotkey("Control+F11"); // Set default hotkey
                onTokenSaved();
            } else {
                setError('Invalid token. Please check and try again.');
            }
        } catch (error) {
            setError('Failed to verify token. Please try again.');
            console.error('Token verification error:', error);
        } finally {
            setIsVerifying(false);
        }
    };

    return (
        <div className="container animate-fade-in">
            <Card variant="elevated" padding="lg">
                <CardHeader>
                    <div className="text-center w-full">
                        <div className="bg-blue-100 p-4 rounded-full inline-flex items-center justify-center mb-4">
                            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                            </svg>
                        </div>
                        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Welcome to Job Tracker</h2>
                        <p className="text-gray-600">Enter your authentication token to get started</p>
                    </div>
                </CardHeader>

                <CardContent>
                    <div className="space-y-4">
                        <Input
                            label="Authentication Token"
                            type="text"
                            value={token}
                            onChange={(e) => setToken(e.target.value)}
                            placeholder="Paste your token here"
                            error={error}
                            leftIcon={
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                                </svg>
                            }
                        />

                        <Button
                            onClick={handleVerifyToken}
                            loading={isVerifying}
                            disabled={!token.trim()}
                            fullWidth
                            size="lg"
                        >
                            {isVerifying ? 'Verifying...' : 'Continue'}
                        </Button>

                        <p className="text-center text-sm text-gray-500">
                            Don't have a token? Visit the main application to generate one.
                        </p>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
};

export default TokenInput;