/* content.css */

/* Dialog container */
.job-tracker-dialog {
    position: fixed;
    z-index: 50;
    border-radius: 0.75rem;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    animation: fade-in 0.3s ease-out;
    padding: 1rem;
    max-width: 28rem;
    box-shadow:
        0 10px 25px -5px rgba(0, 0, 0, 0.1),
        0 8px 10px -6px rgba(0, 0, 0, 0.05);
    will-change: transform, left, top;
}

/* Dialog header */
.job-tracker-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
    margin-bottom: 0.75rem;
}

/* Drag handle */
.job-tracker-handle {
    cursor: grab;
    width: 100%;
    touch-action: none;
}

.job-tracker-handle:active {
    cursor: grabbing;
}

/* Close button */
.job-tracker-close {
    padding: 0.25rem;
    border-radius: 9999px;
    transition: background-color 0.2s ease;
}

.job-tracker-close:hover {
    background-color: #e5e7eb;
}

/* Form field */
.form-field {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

/* Label */
.input-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

/* Input field */
.input-field {
    padding: 0.5rem;
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
    outline: none;
}

.input-field:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Save button */
.save-button {
    width: 100%;
    background-color: #2563eb;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.save-button:hover {
    background-color: #1d4ed8;
}

/* Chip */
.chip {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.625rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #dbeafe;
    color: #1e40af;
}

/* Account item */
.account-item {
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.account-item:hover {
    background-color: #f9fafb;
}

.account-item.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

/* Toast notification */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #f44336;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 10001;
    font-size: 14px;
}

/* Animations */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-fade-in {
    animation: fade-in 0.3s ease-out;
}