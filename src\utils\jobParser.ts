import { Application, FetchDetailError } from "../types";
import getJobManually from "./manualJobParser";
import { buildPrompt, request } from "./aiJobParser";

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const extractFullJobText = (): string => {
    let fullText = '';
    if (window.location.href.includes("indeed.com"))
        fullText = document.getElementsByClassName("jobsearch-JobComponent")[0].textContent || '';
    else fullText = document.body.innerText || '';

    return fullText;
};

const domains = [
  'www.glassdoor.com',
  'www.remoterocketship.com',
  'www.ziprecruiter.com',
  'himalayas.app',
  'jobs.ashbyhq.com',
  'job-boards.greenhouse.io',
  'boards.greenhouse.io',
  'jobs.jobvite.com',
  'jobs.lever.co',
  'myworkdayjobs.com',
  'www.indeed.com',
  'applytojob.com',
  'www.dice.com'
];


// Main function to extract job details
export const parseJobDetails = async (): Promise<Application | FetchDetailError> => {
    try {
        const pageLink = window.location.href;

        if (domains.some(domain => pageLink.includes(domain))) {
            return getJobManually();
        }
        // const pageData = document.body.textContent
        const prompt = buildPrompt(extractFullJobText() || pageLink);
        let parsed;
        for (let i = 0; i < 5; i++) {
            parsed = await request(prompt);
            if (parsed !== null) break;
            await sleep(1000);
        }
        console.log('parsed', parsed);
        if (parsed === null || parsed === undefined || JSON.stringify(parsed) === '{}') {
            console.error("Failed");
            return {
                success: false,
                message: "Failed to fetch job details"
            };
            // const jobDetail = getJobManually();
            // return jobDetail;
        }

        if (parsed.success === false) {
            return parsed;
        }

        return {
            jobRole: parsed.jobTitle || "Unknown Job Title",
            companyName: parsed.companyName || "Unknown Company",
            jobDescription: parsed.jobDescription || "No job description found",
            remoteAvailability: parsed.remoteAvailability || "Not Specified",
            techStacks: parsed.techStacks || [],
            jobUrl: pageLink
        };
    } catch (error) {
        console.error("Failed", error);
        return {
            success: false,
            message: "Failed to fetch job details",
            error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
};