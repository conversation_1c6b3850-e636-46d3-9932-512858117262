import { Account, AuthToken } from "../types";

export const saveAuthToken = async (token: string): Promise<void> => {
    try {
        await chrome.storage.local.set({ authToken: { token } });
        console.info("Auth token saved successfully");
    } catch (error) {
        console.error("Error saving auth token: ", error);
        throw error;
    }
};

export const getAuthToken = async (): Promise<AuthToken | null> => {
    try {
        const result = await chrome.storage.local.get(['authToken']) as { authToken: AuthToken };
        return result.authToken || null;
    } catch (error) {
        console.error("Error getting auth token:", error);
        return null;
    }
};

export const saveSelectedAccount = async (account: Account): Promise<void> => {
    try {
        await chrome.storage.local.set({ selectedAccount: account });
        console.info("Selected Account saved successfully");
    } catch (error) {
        console.error("Error saving selected account:", error);
    }
};

export const getSelectedAccount = async (): Promise<Account | undefined> => {
    try {
        const result = await chrome.storage.local.get(['selectedAccount']) as { selectedAccount: Account };
        return result.selectedAccount || undefined;
    } catch (error) {
        console.error("Error getting selected account:", error);
        return undefined;
    }
};

export const saveHotkey = async (hotkey: string): Promise<void> => {
    try {
        await chrome.storage.local.set({ hotkey });
        console.info("hotkey set successfully");
    } catch (error) {
        console.error("Error saving hotkey:", error);
    }
}

export const getHotkey = async (): Promise<string> => {
    try {
        const result = await chrome.storage.local.get(['hotkey']) as { hotkey: string };
        return result.hotkey || "Control+F11";
    } catch (error) {
        console.error("Error getting hotkey:", error);
        return "Control+F11";
    }
}

export const clearStorage = async (): Promise<void> => {
    try {
        await chrome.storage.local.clear();
        console.log("Storage cleared successfully");
    } catch (error) {
        console.error("Error clearing storage:", error);
        throw error;
    }
};



