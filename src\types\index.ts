// ============================================================================
// CORE TYPES
// ============================================================================

export interface AuthToken {
    token: string;
}

export interface Account {
    email: string;
    nationality?: string;
    address?: string;
    phoneNumber?: string;
    university?: string;
    cv?: string;
    resume?: string;
}

export interface UserInterface {
    username: string;
    name: string;
    accounts: Account[];
    token?: string;
}

// ============================================================================
// APPLICATION TYPES
// ============================================================================

export interface Application {
    companyName: string;
    companyNationality?: string;
    companySite?: string;
    jobRole: string;
    remoteAvailability?: string;
    techStacks: string[];
    jobUrl: string;
    jobDescription?: string;
    accountId?: string;
}

export interface SaveJobParam {
    detail: Application;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}

export interface FetchDetailError {
    success: false;
    message: string;
    error?: string;
}

// ============================================================================
// CHROME EXTENSION TYPES
// ============================================================================

export interface ChromeStorage {
    authToken?: AuthToken;
    selectedAccount?: Account;
    hotkey?: string;
    theme?: string;
}

export enum Sender {
    React = 'React',
    Content = 'Content',
    Background = 'Background'
}

export interface ChromeMessage<T = any> {
    type: string;
    payload?: T;
    from?: Sender;
    tabId?: number;
}

// ============================================================================
// UI/UX TYPES
// ============================================================================

export type ToastType = 'info' | 'success' | 'error' | 'loading';

export interface ToastMessage {
    id: string;
    type: ToastType;
    title: string;
    message: string;
    duration?: number;
    action?: {
        label: string;
        onClick: () => void;
    };
}

export type SetupStage = 'LOADING' | 'TOKEN_INPUT' | 'ACCOUNT_SELECTION' | 'DASHBOARD';

export type RemoteAvailability = 'Remote Only' | 'Hybrid' | 'Remote Possible' | 'On-site' | 'Not Specified';

// ============================================================================
// FORM TYPES
// ============================================================================

export interface TokenFormData {
    token: string;
}

export interface HotkeyFormData {
    hotkey: string;
}

// ============================================================================
// AI TYPES
// ============================================================================

export interface AIJobParseRequest {
    prompt: string;
    isQuestion?: boolean;
}

export interface AIJobParseResponse {
    jobTitle?: string;
    companyName?: string;
    companyNationality?: string;
    companySite?: string;
    remoteAvailability?: RemoteAvailability;
    techStacks?: string[];
    jobDescription?: string;
}