import React from 'react';

const Index = () => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-background to-secondary/50">
      <div className="max-w-md w-full p-8 bg-white rounded-2xl shadow-lg animate-fade-in">
        <div className="text-center mb-6">
          <div className="bg-primary/10 p-4 rounded-full inline-flex items-center justify-center mb-4">
            <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold mb-2">Job Application Tracker</h1>
          <p className="text-muted-foreground">Track your job applications with ease</p>
        </div>
        
        <div className="space-y-4 mb-8">
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">How it works:</h3>
            <ol className="list-decimal pl-5 space-y-2">
              <li>Visit a job posting page</li>
              <li>Press <kbd className="px-2 py-1 bg-gray-200 rounded text-gray-700 mx-1">Ctrl</kbd> + <kbd className="px-2 py-1 bg-gray-200 rounded text-gray-700 mx-1">`</kbd></li>
              <li>Review and save the job details</li>
              <li>Track your applications effortlessly</li>
            </ol>
          </div>
        </div>
        
        <div className="text-center text-sm text-gray-500">
          <p>This is a Chrome extension for tracking job applications.</p>
          <p>Please use the popup interface in your browser toolbar.</p>
        </div>
      </div>
    </div>
  );
};

export default Index;