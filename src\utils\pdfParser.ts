import * as pdfjsLib from 'pdfjs-dist';
import { API_CONFIG } from '../constants';

pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('pdf.worker.min.mjs');

async function fetchAndparsePdf(url: string) {
    const response = await fetch(`${API_CONFIG.baseUrl}/../../${url}`);
    const arrayBuffer = await response.arrayBuffer();

    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

    const text = [];
    for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const content = await page.getTextContent();
        text.push(content.items
            .map((item) => ('str' in item ? item.str : ''))
            .join(' '));
    }
    return text.join('\n');
}

export default fetchAndparsePdf;