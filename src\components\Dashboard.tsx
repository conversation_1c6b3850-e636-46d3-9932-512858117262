import { useEffect, useState, type FC } from "react";
import { Account } from "../types";
import { clearAllStorage, getHotkey, getSelectedAccount, saveHotkey } from "../utils/storage";
import { <PERSON><PERSON>, Card, Loading } from "./ui";
import Settings from "./Settings";

const Dashboard: FC = () => {
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [hotkey, setHotKey] = useState("Control+F11");
  const [recording, setRecording] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadSelectedAccount = async () => {
      try {
        setIsLoading(true);
        const account = await getSelectedAccount();
        setSelectedAccount(account);
      } catch (error) {
        console.error('Error loading selected Account: ', error);
      } finally {
        setIsLoading(false);
      }
    }
    loadSelectedAccount();
  }, []);

  useEffect(() => {
    getHotkey().then(setHotKey);
  }, []);

  // Focus the hotkey section when recording starts
  useEffect(() => {
    if (recording) {
      const hotkeySection = document.querySelector('[data-hotkey-recorder="true"]') as HTMLElement;
      if (hotkeySection) {
        hotkeySection.focus();
      }
    }
  }, [recording]);

  const handleKeyDown = async (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!recording) return;

    e.preventDefault();
    e.stopPropagation();

    // Ignore modifier-only keys
    if (["Control", "Alt", "Shift", "Meta"].includes(e.key)) {
      return;
    }

    // Handle Escape key to cancel recording
    if (e.key === "Escape") {
      setRecording(false);
      return;
    }

    const combination = `${e.ctrlKey ? 'Control+' : ''}${e.shiftKey ? 'Shift+' : ''}${e.altKey ? 'Alt+' : ''}${e.key}`;

    try {
      // Only update if it's different from current hotkey
      if (combination !== hotkey) {
        setHotKey(combination);
        await saveHotkey(combination);

        // Notify background script to broadcast hotkey update
        chrome.runtime.sendMessage({
          type: 'HOTKEY_UPDATED',
          hotkey: combination
        });

        console.log(`Hotkey updated to: ${combination}`);
      }
    } catch (error) {
      console.error('Error saving hotkey:', error);
    }

    setRecording(false); // Stop recording after one valid key combo
  };

  const handleStartRecording = () => {
    setRecording(true);
  };


  const handleTriggerDialog = () => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]?.id) {
        // Check if the current tab is a valid web page (not chrome:// or extension pages)
        if (tabs[0].url && !tabs[0].url.startsWith('chrome://') && !tabs[0].url.startsWith('chrome-extension://')) {
          chrome.tabs.sendMessage(tabs[0].id, { type: 'SHOW_DIALOG' }, (response) => {
            // Handle potential errors (e.g., content script not loaded)
            if (chrome.runtime.lastError) {
              console.log('Content script not available on this page');
              // Could show a toast or notification here
            }
          });
        } else {
          console.log('Cannot run extension on this type of page');
          // Could show a toast notification that this page type is not supported
        }
      }
    });
  };

  const handleLogout = async () => {
    try {
      await clearAllStorage();
      window.location.reload();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  }

  if (isLoading) {
    return (
      <div className="container">
        <Card variant="elevated" padding="lg">
          <Loading text="Loading dashboard..." size="lg" />
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto p-4 animate-fade-in">
      {selectedAccount ? (
        <Card variant="elevated" padding="none" className="overflow-hidden">
          <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 px-6 py-8">
            <Settings />
            <div className="text-center">
              <div className="bg-white/80 backdrop-blur-sm p-3 rounded-2xl inline-flex items-center justify-center mb-4 shadow-sm">
                <svg className="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Job Tracker</h1>
              <p className="text-gray-600 text-sm">Track your job applications with ease</p>
            </div>
          </div>

          <div className="p-6 space-y-6">

            {/* Account Section */}
            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-sm">
                  <span className="text-white font-semibold text-lg">
                    {selectedAccount?.email?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-gray-900 text-sm">Account</h3>
                  <p className="text-sm text-gray-600 truncate">{selectedAccount?.email}</p>
                </div>
              </div>
            </div>
            {/* Hotkey Section */}
            <div
              className={`bg-gray-50 rounded-xl p-4 transition-all duration-200 ${recording ? 'ring-2 ring-red-500 ring-opacity-50' : ''}`}
              tabIndex={recording ? 0 : -1}
              onKeyDown={recording ? handleKeyDown : undefined}
              data-hotkey-recorder={recording ? "true" : "false"}
              style={{ outline: 'none' }}
            >
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-gray-900 text-sm">Hotkey</h3>
                <Button
                  variant={recording ? "danger" : "outline"}
                  size="sm"
                  onClick={recording ? () => setRecording(false) : handleStartRecording}
                  className="text-xs px-3 py-1.5"
                >
                  {recording ? 'Cancel' : 'Change'}
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                {hotkey.split('+').map((key, index) => (
                  <span key={key} className="inline-flex items-center">
                    {index > 0 && <span className="text-gray-400 mx-1">+</span>}
                    <kbd className="px-2 py-1 bg-white border border-gray-200 rounded text-xs font-mono text-gray-700 shadow-sm">
                      {key}
                    </kbd>
                  </span>
                ))}
              </div>
              {recording && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg animate-pulse">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-ping"></div>
                    <p className="text-xs text-red-700 font-medium">
                      Recording hotkey...
                    </p>
                  </div>
                  <p className="text-xs text-red-600">
                    🎯 Press any key combination (e.g., Ctrl+Shift+J)
                  </p>
                  <p className="text-xs text-red-500 mt-1">
                    Press Escape to cancel
                  </p>
                </div>
              )}
            </div>
            {/* Action Button */}
            <Button
              onClick={handleTriggerDialog}
              fullWidth
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Open Job Tracker
            </Button>

            {/* Status Info */}
            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <p className="text-sm font-medium text-green-800">Extension Ready</p>
              </div>
              <p className="text-xs text-green-700 mt-1">
                Press your hotkey on any job page to start tracking
              </p>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t bg-gray-50 px-6 py-4">
            <Button
              variant="ghost"
              onClick={handleLogout}
              fullWidth
              className="text-gray-600 hover:text-gray-800 font-medium"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Log Out
            </Button>
          </div>
        </Card>
      ) : (
        <Card variant="elevated" padding="lg">
          <div className="text-center py-8">
            <div className="bg-red-100 p-4 rounded-full inline-flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Account Not Found</h3>
            <p className="text-gray-600 mb-6">There was an error loading your account information.</p>
            <Button
              onClick={handleLogout}
              variant="primary"
              size="lg"
            >
              Restart Setup
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};

export default Dashboard;