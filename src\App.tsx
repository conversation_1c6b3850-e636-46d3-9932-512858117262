
import "./styles/globals.css";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { getAuthToken, getSelectedAccount } from "./utils/storage";
import TokenInput from "./components/TokenInput";
import AccountSelection from "./components/AccountSelection";
import Dashboard from "./components/Dashboard";
import { SETUP_STAGES } from "./constants";
import { ThemeProvider } from "./contexts/ThemeContext";
import ErrorBoundary from "./components/ErrorBoundary";

const queryClient = new QueryClient();

const App = () => {
  const [setupStage, setSetupStage] = useState<keyof typeof SETUP_STAGES>('LOADING');

  useEffect(() => {
    const checkSetupStage = async () => {
      try {
        const authToken = await getAuthToken();

        if (!authToken) {
          setSetupStage('TOKEN_INPUT');
          return;
        }

        const selectedAccount = await getSelectedAccount();

        if (!selectedAccount) {
          setSetupStage('ACCOUNT_SELECTION');
          return;
        }
        setSetupStage('DASHBOARD');
      } catch (error) {
        console.error('Error checking setup state:', error);
        setSetupStage('TOKEN_INPUT');
      }
    }

    checkSetupStage();
  }, []);

  const handleTokenSaved = () => {
    setSetupStage('ACCOUNT_SELECTION');
  };

  const handleAccountSelected = () => {
    setSetupStage('DASHBOARD');
  };

  const renderContent = () => {
    switch (setupStage) {
      case 'LOADING':
        return (
          <div className="flex flex-col items-center justify-center h-64 animate-fade-in">
            <div className="animate-spin w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full mb-4"></div>
            <p className="text-gray-500">Loading...</p>
          </div>
        );
      case 'TOKEN_INPUT':
        return <TokenInput onTokenSaved={handleTokenSaved} />;
      case 'ACCOUNT_SELECTION':
        return <AccountSelection onAccountSelected={handleAccountSelected} />;
      case 'DASHBOARD':
        return <Dashboard />;
      default:
        return <div>Something went wrong</div>;
    }
  };

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <QueryClientProvider client={queryClient}>
          <div className="min-w-[350px] min-h-[400px] max-w-md mx-auto bg-bg-primary text-text-primary">
            {renderContent()}
          </div>
        </QueryClientProvider>
      </ThemeProvider>
    </ErrorBoundary>
  )
};

export default App;