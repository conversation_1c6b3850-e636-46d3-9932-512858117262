(()=>{"use strict";const e="authToken",t="selectedAccount",r="serverUrl",o="aiServerUrl",n="http://192.168.13.99:8001/api/extension",s="http://192.168.13.221:11434",a="SAVE_JOB",c="CHECK_CONTENT_LOADED",i="AI_JOB_PARSE",l="HOTKEY_UPDATED",u="SHOW_TOAST",d="gemma3:4b",h=.1;class p extends Error{constructor(e,t,r){super(e),this.code=t,this.statusCode=r,this.name="AppError"}}const m=async(e,t)=>{try{var r;const o=await chrome.storage.local.get([e]);return(e=>{if(chrome.runtime.lastError)throw console.error(`Chrome ${e} error:`,chrome.runtime.lastError),new p(`Chrome extension error during ${e}: ${chrome.runtime.lastError.message}`,"CHROME_ERROR")})("storage.get"),null!==(r=o[e])&&void 0!==r?r:t}catch(o){return((e,t,r)=>{console.group(`\ud83d\udea8 Error in ${t}`),console.error("Error:",e),console.error("Stack:",e.stack),r&&console.error("Additional data:",r),console.groupEnd()})(o,`getStorageItem(${e})`),t}},g=async()=>{const t=await m(e);return null!==t&&void 0!==t?t:null},y=async()=>{const e=await m(t);return null!==e&&void 0!==e?e:null},w=async()=>await m(r,n)||n,f=async e=>{const{url:t,options:r,token:o}=e,n={"Content-Type":"application/json",...r.headers};try{const e=await w(),s=await fetch(`${e}${t}?apiKey=${o}`,{...r,headers:n});if(!s.ok){const e=await s.json();throw new Error(e.message||"API request failed")}return(await s.json()).data}catch(s){throw console.error("API request error:",s),s}},b=async(e,t)=>f({url:"/application",options:{method:"POST",body:JSON.stringify({data:{...e,stack:e.techStacks.join(", ")}})},token:t.token}),k=async e=>f({url:"/application/getlastjob-description",options:{method:"GET"},token:e.token});chrome.runtime.onMessage.addListener(((e,t,r)=>{return e.type===a?(T(e.payload).then((e=>r(e))).catch((e=>{console.error("Error saving Job: ",e),r({success:!1,message:e.message})})),!0):e.type===c?(chrome.scripting.executeScript({target:{tabId:(null===(o=t.tab)||void 0===o?void 0:o.id)||0},func:()=>window.contentScriptLoaded||!1},(e=>{var t;chrome.runtime.lastError?(console.error("Error checking content script:",chrome.runtime.lastError),r(!1)):r((null===e||void 0===e||null===(t=e[0])||void 0===t?void 0:t.result)||!1)})),!0):e.type===i?(E(e.prompt,e.isQuestion).then((e=>r(e))).catch((e=>{console.error("Error parsing job with AI:",e),r({success:!1,error:e.message})})),!0):e.type===l?(v(e.hotkey),!0):void 0;var o}));const v=async e=>{try{const t=await chrome.tabs.query({});for(const r of t)r.id&&r.url&&!r.url.startsWith("chrome://")&&!r.url.startsWith("chrome-extension://")&&chrome.tabs.sendMessage(r.id,{type:l,hotkey:e}).catch((()=>{}))}catch(t){console.error("Error broadcasting hotkey update:",t)}},E=async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{const c=t?"You are a helpful job Apply assistant answering questions of jobs with my resume":"You are a helpful JSON parser assistant parseing job details from text as JSON object.",i=await(async()=>await m(o,s)||s)(),l=await fetch(`${i}/api/chat`,{method:"POST",headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*"},body:JSON.stringify({model:d,messages:[{role:"system",content:c},{role:"user",content:e}],temperature:h,stream:!1})});if(!l.ok)throw new Error(`HTTP error! status: ${l.status}`);const u=await l.text();let p;console.log("Raw response:",u);try{p=JSON.parse(u)}catch(n){const e=u.trim().split("\n"),t=e[e.length-1];p=JSON.parse(t)}const g=p.message;if(!g)throw new Error("No message in completion");var r;if(t)return null!==(r=g.content)&&void 0!==r?r:"";let y=g.content.trim();y.startsWith("```")&&(y=y.replace(/^```[a-z]*\n/,"").replace(/```$/,""));try{return JSON.parse(y)}catch(a){throw console.error("Failed to parse JSON:",a,y),a}}catch(c){throw console.error("AI Job Parse Error:",c),c}},T=async e=>{try{const t=await g(),r=await y();return t?r?(await b({...e,accountId:r.email},t),{success:!0,message:"Job saved successfully"}):{success:!1,message:"No account selected"}:{success:!1,message:"Authentication token not found"}}catch(t){return console.error("Error saving job:",t),{success:!1,message:t instanceof Error?t.message:"Unknown error occurred"}}};chrome.runtime.onInstalled.addListener((()=>{console.log("Job Tracker Extension installed"),chrome.contextMenus.create({id:"runTrack",title:"Pick up the detail from the page",contexts:["all"]}),chrome.contextMenus.create({id:"getAnswerForSelectedText",title:"Process selected Text",contexts:["selection"]})})),chrome.contextMenus.onClicked.addListener((async(e,t)=>{t&&t.id&&("runTrack"===e.menuItemId&&chrome.scripting.executeScript({target:{tabId:t.id},func:()=>{window.runTracker&&window.runTracker()}}),"getAnswerForSelectedText"===e.menuItemId&&e.selectionText&&await async function(e,t){const r=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3;chrome.tabs.sendMessage(t,{type:u,message:e,toastType:r,duration:o}).catch((()=>{}))};try{r("Generating AI response for selected text...","loading",0);const o=await g(),n=await y();if(!o)return void r("Authentication token not found. Please check your settings.","error");if(!n)return void r("No account selected. Please check your settings.","error");const s=await k(o);if(!s||!s.jobDescription)return void r("No job description found. Please save a job first.","error");const a=`\n            You are a helpful job Apply assistant answering questions of jobs with my resume.\n            Your job is to answer job-related questions as if you're me, using my resume and the job description.\n            The answers should sound confident, professional, and human \u2014 not robotic or generic.\n            Be concise and specific. Only mention experience or skills if they're relevant to both the question and the job description.\n            And never include your voice like "here is the answer for you" or "Please let me know if you have another question" etc.\n            Only return result as my answer which I can directly use without removing such a thing.\n            Here is the job description:\n            // Job description start\n            ${s.jobDescription}\n            // Job description ends\n            ${n.resume&&`Here is my resume\n            // Resume go here\n            ${n.resume}\n            // Resume ends`}\n            Now answer the question as me: ${e}\n        `,c=await E(a,!0);if(!c)return void r("Failed to generate AI response. Please try again.","error");await chrome.scripting.executeScript({target:{tabId:t},func:e=>navigator.clipboard.writeText(e||"").then((()=>!0)).catch((e=>(console.error("Clipboard write failed:",e),!1))),args:[c]}),r("AI response generated and copied to clipboard!","success")}catch(o){console.error("Error in processAndCopyWithFeedback:",o),r("Failed to process request. Please try again.","error")}}(e.selectionText,t.id))})),console.log("Job Tracker Background Script Loaded")})();
//# sourceMappingURL=background.js.map