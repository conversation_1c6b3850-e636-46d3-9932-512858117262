/*! For license information please see content.js.LICENSE.txt */
(()=>{var e={497:(e,t,n)=>{"use strict";var r=n(218);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,a,i){if(i!==r){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return n.PropTypes=n,n}},173:(e,t,n)=>{e.exports=n(497)()},218:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},730:(e,t,n)=>{"use strict";var r=n(43),o=n(853);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(l[e]=t,e=0;e<t.length;e++)i.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,o,a,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var b=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function v(e,t,n,r){var o=g.hasOwnProperty(t)?g[t]:null;(null!==o?0!==o.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(b,y);g[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(b,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(b,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var k=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,x=Symbol.for("react.element"),w=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),j=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),_=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),L=Symbol.for("react.memo"),z=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var D=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var R=Symbol.iterator;function M(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=R&&e[R]||e["@@iterator"])?e:null}var O,I=Object.assign;function F(e){if(void 0===O)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);O=t&&t[1]||""}return"\n"+O+e}var $=!1;function U(e,t){if(!e||$)return"";$=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var o=u.stack.split("\n"),a=r.stack.split("\n"),i=o.length-1,l=a.length-1;1<=i&&0<=l&&o[i]!==a[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==a[l]){if(1!==i||1!==l)do{if(i--,0>--l||o[i]!==a[l]){var s="\n"+o[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=l);break}}}finally{$=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function A(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=U(e.type,!1);case 11:return e=U(e.type.render,!1);case 1:return e=U(e.type,!0);default:return""}}function B(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case w:return"Portal";case E:return"Profiler";case j:return"StrictMode";case P:return"Suspense";case T:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case _:return(e.displayName||"Context")+".Consumer";case C:return(e._context.displayName||"Context")+".Provider";case N:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case L:return null!==(t=e.displayName||null)?t:B(e.type)||"Memo";case z:t=e._payload,e=e._init;try{return B(e(t))}catch(n){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return B(t);case 8:return t===j?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function W(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function V(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function G(e){e._valueTracker||(e._valueTracker=function(e){var t=V(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=V(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Y(e,t){var n=t.checked;return I({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=W(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function K(e,t){null!=(t=t.checked)&&v(e,"checked",t,!1)}function J(e,t){K(e,t);var n=W(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,W(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&Q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+W(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return I({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:W(n)}}function ae(e,t){var n=W(t.value),r=W(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var be=I({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(be[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function ve(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ke=null;function xe(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var we=null,Se=null,je=null;function Ee(e){if(e=ko(e)){if("function"!==typeof we)throw Error(a(280));var t=e.stateNode;t&&(t=wo(t),we(e.stateNode,e.type,t))}}function Ce(e){Se?je?je.push(e):je=[e]:Se=e}function _e(){if(Se){var e=Se,t=je;if(je=Se=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function Ne(e,t){return e(t)}function Pe(){}var Te=!1;function Le(e,t,n){if(Te)return e(t,n);Te=!0;try{return Ne(e,t,n)}finally{Te=!1,(null!==Se||null!==je)&&(Pe(),_e())}}function ze(e,t){var n=e.stateNode;if(null===n)return null;var r=wo(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var De=!1;if(c)try{var Re={};Object.defineProperty(Re,"passive",{get:function(){De=!0}}),window.addEventListener("test",Re,Re),window.removeEventListener("test",Re,Re)}catch(ce){De=!1}function Me(e,t,n,r,o,a,i,l,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Oe=!1,Ie=null,Fe=!1,$e=null,Ue={onError:function(e){Oe=!0,Ie=e}};function Ae(e,t,n,r,o,a,i,l,s){Oe=!1,Ie=null,Me.apply(Ue,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function We(e){if(Be(e)!==e)throw Error(a(188))}function Ve(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return We(o),e;if(i===r)return We(o),t;i=i.sibling}throw Error(a(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?Ge(e):null}function Ge(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ge(e);if(null!==t)return t;e=e.sibling}return null}var qe=o.unstable_scheduleCallback,Qe=o.unstable_cancelCallback,Ye=o.unstable_shouldYield,Xe=o.unstable_requestPaint,Ke=o.unstable_now,Je=o.unstable_getCurrentPriorityLevel,Ze=o.unstable_ImmediatePriority,et=o.unstable_UserBlockingPriority,tt=o.unstable_NormalPriority,nt=o.unstable_LowPriority,rt=o.unstable_IdlePriority,ot=null,at=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/st|0)|0},lt=Math.log,st=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,i=268435455&n;if(0!==i){var l=i&~o;0!==l?r=dt(l):0!==(a&=i)&&(r=dt(a))}else 0!==(i=n&~o)?r=dt(i):0!==a&&(r=dt(a));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(a=t&-t)||16===o&&0!==(4194240&a)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-it(t)),r|=e[n],t&=~o;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function bt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var vt=0;function kt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var xt,wt,St,jt,Et,Ct=!1,_t=[],Nt=null,Pt=null,Tt=null,Lt=new Map,zt=new Map,Dt=[],Rt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mt(e,t){switch(e){case"focusin":case"focusout":Nt=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":Lt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":zt.delete(t.pointerId)}}function Ot(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=ko(t))&&wt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function It(e){var t=vo(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void Et(e.priority,(function(){St(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ft(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Yt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ko(n))&&wt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);ke=r,n.target.dispatchEvent(r),ke=null,t.shift()}return!0}function $t(e,t,n){Ft(e)&&n.delete(t)}function Ut(){Ct=!1,null!==Nt&&Ft(Nt)&&(Nt=null),null!==Pt&&Ft(Pt)&&(Pt=null),null!==Tt&&Ft(Tt)&&(Tt=null),Lt.forEach($t),zt.forEach($t)}function At(e,t){e.blockedOn===t&&(e.blockedOn=null,Ct||(Ct=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,Ut)))}function Bt(e){function t(t){return At(t,e)}if(0<_t.length){At(_t[0],e);for(var n=1;n<_t.length;n++){var r=_t[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Nt&&At(Nt,e),null!==Pt&&At(Pt,e),null!==Tt&&At(Tt,e),Lt.forEach(t),zt.forEach(t),n=0;n<Dt.length;n++)(r=Dt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Dt.length&&null===(n=Dt[0]).blockedOn;)It(n),null===n.blockedOn&&Dt.shift()}var Ht=k.ReactCurrentBatchConfig,Wt=!0;function Vt(e,t,n,r){var o=vt,a=Ht.transition;Ht.transition=null;try{vt=1,qt(e,t,n,r)}finally{vt=o,Ht.transition=a}}function Gt(e,t,n,r){var o=vt,a=Ht.transition;Ht.transition=null;try{vt=4,qt(e,t,n,r)}finally{vt=o,Ht.transition=a}}function qt(e,t,n,r){if(Wt){var o=Yt(e,t,n,r);if(null===o)Wr(e,t,r,Qt,n),Mt(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Nt=Ot(Nt,e,t,n,r,o),!0;case"dragenter":return Pt=Ot(Pt,e,t,n,r,o),!0;case"mouseover":return Tt=Ot(Tt,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return Lt.set(a,Ot(Lt.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,zt.set(a,Ot(zt.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(Mt(e,r),4&t&&-1<Rt.indexOf(e)){for(;null!==o;){var a=ko(o);if(null!==a&&xt(a),null===(a=Yt(e,t,n,r))&&Wr(e,t,r,Qt,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Wr(e,t,r,null,n)}}var Qt=null;function Yt(e,t,n,r){if(Qt=null,null!==(e=vo(e=xe(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Je()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Kt=null,Jt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Jt,r=n.length,o="value"in Kt?Kt.value:Kt.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Zt=o.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return I(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,ln,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=on(un),dn=I({},un,{view:0,detail:0}),fn=on(dn),pn=I({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:En,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(an=e.screenX-sn.screenX,ln=e.screenY-sn.screenY):ln=an=0,sn=e),an)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),hn=on(pn),mn=on(I({},pn,{dataTransfer:0})),gn=on(I({},dn,{relatedTarget:0})),bn=on(I({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=I({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vn=on(yn),kn=on(I({},un,{data:0})),xn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function En(){return jn}var Cn=I({},dn,{key:function(e){if(e.key){var t=xn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?wn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:En,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),_n=on(Cn),Nn=on(I({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pn=on(I({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:En})),Tn=on(I({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Ln=I({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),zn=on(Ln),Dn=[9,13,27,32],Rn=c&&"CompositionEvent"in window,Mn=null;c&&"documentMode"in document&&(Mn=document.documentMode);var On=c&&"TextEvent"in window&&!Mn,In=c&&(!Rn||Mn&&8<Mn&&11>=Mn),Fn=String.fromCharCode(32),$n=!1;function Un(e,t){switch(e){case"keyup":return-1!==Dn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function An(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Bn=!1;var Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function Vn(e,t,n,r){Ce(r),0<(t=Gr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Gn=null,qn=null;function Qn(e){Fr(e,0)}function Yn(e){if(q(xo(e)))return e}function Xn(e,t){if("change"===e)return t}var Kn=!1;if(c){var Jn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Jn=Zn}else Jn=!1;Kn=Jn&&(!document.documentMode||9<document.documentMode)}function tr(){Gn&&(Gn.detachEvent("onpropertychange",nr),qn=Gn=null)}function nr(e){if("value"===e.propertyName&&Yn(qn)){var t=[];Vn(t,qn,e,xe(e)),Le(Qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),qn=n,(Gn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yn(qn)}function ar(e,t){if("click"===e)return Yn(t)}function ir(e,t){if("input"===e||"change"===e)return Yn(t)}var lr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(lr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!d.call(t,o)||!lr(e[o],t[o]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Q((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=n.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!e.extend&&a>r&&(o=r,r=a,a=o),o=cr(n,a);var i=cr(n,r);o&&i&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,br=null,yr=null,vr=!1;function kr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;vr||null==gr||gr!==Q(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&sr(yr,r)||(yr=r,0<(r=Gr(br,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function xr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var wr={animationend:xr("Animation","AnimationEnd"),animationiteration:xr("Animation","AnimationIteration"),animationstart:xr("Animation","AnimationStart"),transitionend:xr("Transition","TransitionEnd")},Sr={},jr={};function Er(e){if(Sr[e])return Sr[e];if(!wr[e])return e;var t,n=wr[e];for(t in n)if(n.hasOwnProperty(t)&&t in jr)return Sr[e]=n[t];return e}c&&(jr=document.createElement("div").style,"AnimationEvent"in window||(delete wr.animationend.animation,delete wr.animationiteration.animation,delete wr.animationstart.animation),"TransitionEvent"in window||delete wr.transitionend.transition);var Cr=Er("animationend"),_r=Er("animationiteration"),Nr=Er("animationstart"),Pr=Er("transitionend"),Tr=new Map,Lr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function zr(e,t){Tr.set(e,t),s(t,[e])}for(var Dr=0;Dr<Lr.length;Dr++){var Rr=Lr[Dr];zr(Rr.toLowerCase(),"on"+(Rr[0].toUpperCase()+Rr.slice(1)))}zr(Cr,"onAnimationEnd"),zr(_r,"onAnimationIteration"),zr(Nr,"onAnimationStart"),zr("dblclick","onDoubleClick"),zr("focusin","onFocus"),zr("focusout","onBlur"),zr(Pr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Or=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mr));function Ir(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,i,l,s,u){if(Ae.apply(this,arguments),Oe){if(!Oe)throw Error(a(198));var c=Ie;Oe=!1,Ie=null,Fe||(Fe=!0,$e=c)}}(r,t,void 0,e),e.currentTarget=null}function Fr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,u=l.currentTarget;if(l=l.listener,s!==a&&o.isPropagationStopped())break e;Ir(o,l,u),a=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,u=l.currentTarget,l=l.listener,s!==a&&o.isPropagationStopped())break e;Ir(o,l,u),a=s}}}if(Fe)throw e=$e,Fe=!1,$e=null,e}function $r(e,t){var n=t[go];void 0===n&&(n=t[go]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function Ur(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Ar="_reactListening"+Math.random().toString(36).slice(2);function Br(e){if(!e[Ar]){e[Ar]=!0,i.forEach((function(t){"selectionchange"!==t&&(Or.has(t)||Ur(t,!1,e),Ur(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ar]||(t[Ar]=!0,Ur("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Xt(t)){case 1:var o=Vt;break;case 4:o=Gt;break;default:o=qt}n=o.bind(null,t,n,e),o=void 0,!De||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Wr(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;i=i.return}for(;null!==l;){if(null===(i=vo(l)))return;if(5===(s=i.tag)||6===s){r=a=i;continue e}l=l.parentNode}}r=r.return}Le((function(){var r=a,o=xe(n),i=[];e:{var l=Tr.get(e);if(void 0!==l){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=_n;break;case"focusin":u="focus",s=gn;break;case"focusout":u="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Pn;break;case Cr:case _r:case Nr:s=bn;break;case Pr:s=Tn;break;case"scroll":s=fn;break;case"wheel":s=zn;break;case"copy":case"cut":case"paste":s=vn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Nn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==l?l+"Capture":null:l;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=ze(h,f))&&c.push(Vr(h,m,p)))),d)break;h=h.return}0<c.length&&(l=new s(l,u,null,n,o),i.push({event:l,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===ke||!(u=n.relatedTarget||n.fromElement)||!vo(u)&&!u[mo])&&(s||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?vo(u):null)&&(u!==(d=Be(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Nn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?l:xo(s),p=null==u?l:xo(u),(l=new c(m,h+"leave",s,n,o)).target=d,l.relatedTarget=p,m=null,vo(o)===r&&((c=new c(f,h+"enter",u,n,o)).target=p,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,h=0,p=c=s;p;p=qr(p))h++;for(p=0,m=f;m;m=qr(m))p++;for(;0<h-p;)c=qr(c),h--;for(;0<p-h;)f=qr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=qr(c),f=qr(f)}c=null}else c=null;null!==s&&Qr(i,l,s,c,!1),null!==u&&null!==d&&Qr(i,d,u,c,!0)}if("select"===(s=(l=r?xo(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var g=Xn;else if(Wn(l))if(Kn)g=ir;else{g=or;var b=rr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(g=ar);switch(g&&(g=g(e,r))?Vn(i,g,n,o):(b&&b(e,l,r),"focusout"===e&&(b=l._wrapperState)&&b.controlled&&"number"===l.type&&ee(l,"number",l.value)),b=r?xo(r):window,e){case"focusin":(Wn(b)||"true"===b.contentEditable)&&(gr=b,br=r,yr=null);break;case"focusout":yr=br=gr=null;break;case"mousedown":vr=!0;break;case"contextmenu":case"mouseup":case"dragend":vr=!1,kr(i,n,o);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":kr(i,n,o)}var y;if(Rn)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else Bn?Un(e,n)&&(v="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(v="onCompositionStart");v&&(In&&"ko"!==n.locale&&(Bn||"onCompositionStart"!==v?"onCompositionEnd"===v&&Bn&&(y=en()):(Jt="value"in(Kt=o)?Kt.value:Kt.textContent,Bn=!0)),0<(b=Gr(r,v)).length&&(v=new kn(v,e,null,n,o),i.push({event:v,listeners:b}),y?v.data=y:null!==(y=An(n))&&(v.data=y))),(y=On?function(e,t){switch(e){case"compositionend":return An(t);case"keypress":return 32!==t.which?null:($n=!0,Fn);case"textInput":return(e=t.data)===Fn&&$n?null:e;default:return null}}(e,n):function(e,t){if(Bn)return"compositionend"===e||!Rn&&Un(e,t)?(e=en(),Zt=Jt=Kt=null,Bn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return In&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Gr(r,"onBeforeInput")).length&&(o=new kn("onBeforeInput","beforeinput",null,n,o),i.push({event:o,listeners:r}),o.data=y))}Fr(i,t)}))}function Vr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gr(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5===o.tag&&null!==a&&(o=a,null!=(a=ze(e,n))&&r.unshift(Vr(e,a,o)),null!=(a=ze(e,t))&&r.push(Vr(e,a,o))),e=e.return}return r}function qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Qr(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,u=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==u&&(l=u,o?null!=(s=ze(n,a))&&i.unshift(Vr(n,s,l)):o||null!=(s=ze(n,a))&&i.push(Vr(n,s,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Yr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Kr(e){return("string"===typeof e?e:""+e).replace(Yr,"\n").replace(Xr,"")}function Jr(e,t,n){if(t=Kr(t),Kr(e)!==t&&n)throw Error(a(425))}function Zr(){}var eo=null,to=null;function no(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ro="function"===typeof setTimeout?setTimeout:void 0,oo="function"===typeof clearTimeout?clearTimeout:void 0,ao="function"===typeof Promise?Promise:void 0,io="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ao?function(e){return ao.resolve(null).then(e).catch(lo)}:ro;function lo(e){setTimeout((function(){throw e}))}function so(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Bt(t)}function uo(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function co(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fo=Math.random().toString(36).slice(2),po="__reactFiber$"+fo,ho="__reactProps$"+fo,mo="__reactContainer$"+fo,go="__reactEvents$"+fo,bo="__reactListeners$"+fo,yo="__reactHandles$"+fo;function vo(e){var t=e[po];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mo]||n[po]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=co(e);null!==e;){if(n=e[po])return n;e=co(e)}return t}n=(e=n).parentNode}return null}function ko(e){return!(e=e[po]||e[mo])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xo(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function wo(e){return e[ho]||null}var So=[],jo=-1;function Eo(e){return{current:e}}function Co(e){0>jo||(e.current=So[jo],So[jo]=null,jo--)}function _o(e,t){jo++,So[jo]=e.current,e.current=t}var No={},Po=Eo(No),To=Eo(!1),Lo=No;function zo(e,t){var n=e.type.contextTypes;if(!n)return No;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function Do(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ro(){Co(To),Co(Po)}function Mo(e,t,n){if(Po.current!==No)throw Error(a(168));_o(Po,t),_o(To,n)}function Oo(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(a(108,H(e)||"Unknown",o));return I({},n,r)}function Io(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||No,Lo=Po.current,_o(Po,e),_o(To,To.current),!0}function Fo(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=Oo(e,t,Lo),r.__reactInternalMemoizedMergedChildContext=e,Co(To),Co(Po),_o(Po,e)):Co(To),_o(To,n)}var $o=null,Uo=!1,Ao=!1;function Bo(e){null===$o?$o=[e]:$o.push(e)}function Ho(){if(!Ao&&null!==$o){Ao=!0;var e=0,t=vt;try{var n=$o;for(vt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}$o=null,Uo=!1}catch(o){throw null!==$o&&($o=$o.slice(e+1)),qe(Ze,Ho),o}finally{vt=t,Ao=!1}}return null}var Wo=[],Vo=0,Go=null,qo=0,Qo=[],Yo=0,Xo=null,Ko=1,Jo="";function Zo(e,t){Wo[Vo++]=qo,Wo[Vo++]=Go,Go=e,qo=t}function ea(e,t,n){Qo[Yo++]=Ko,Qo[Yo++]=Jo,Qo[Yo++]=Xo,Xo=e;var r=Ko;e=Jo;var o=32-it(r)-1;r&=~(1<<o),n+=1;var a=32-it(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Ko=1<<32-it(t)+o|n<<o|r,Jo=a+e}else Ko=1<<a|n<<o|r,Jo=e}function ta(e){null!==e.return&&(Zo(e,1),ea(e,1,0))}function na(e){for(;e===Go;)Go=Wo[--Vo],Wo[Vo]=null,qo=Wo[--Vo],Wo[Vo]=null;for(;e===Xo;)Xo=Qo[--Yo],Qo[Yo]=null,Jo=Qo[--Yo],Qo[Yo]=null,Ko=Qo[--Yo],Qo[Yo]=null}var ra=null,oa=null,aa=!1,ia=null;function la(e,t){var n=zu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function sa(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ra=e,oa=uo(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ra=e,oa=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Xo?{id:Ko,overflow:Jo}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=zu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ra=e,oa=null,!0);default:return!1}}function ua(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ca(e){if(aa){var t=oa;if(t){var n=t;if(!sa(e,t)){if(ua(e))throw Error(a(418));t=uo(n.nextSibling);var r=ra;t&&sa(e,t)?la(r,n):(e.flags=-4097&e.flags|2,aa=!1,ra=e)}}else{if(ua(e))throw Error(a(418));e.flags=-4097&e.flags|2,aa=!1,ra=e}}}function da(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ra=e}function fa(e){if(e!==ra)return!1;if(!aa)return da(e),aa=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!no(e.type,e.memoizedProps)),t&&(t=oa)){if(ua(e))throw pa(),Error(a(418));for(;t;)la(e,t),t=uo(t.nextSibling)}if(da(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){oa=uo(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}oa=null}}else oa=ra?uo(e.stateNode.nextSibling):null;return!0}function pa(){for(var e=oa;e;)e=uo(e.nextSibling)}function ha(){oa=ra=null,aa=!1}function ma(e){null===ia?ia=[e]:ia.push(e)}var ga=k.ReactCurrentBatchConfig;function ba(e,t){if(e&&e.defaultProps){for(var n in t=I({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}var ya=Eo(null),va=null,ka=null,xa=null;function wa(){xa=ka=va=null}function Sa(e){var t=ya.current;Co(ya),e._currentValue=t}function ja(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ea(e,t){va=e,xa=ka=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(kl=!0),e.firstContext=null)}function Ca(e){var t=e._currentValue;if(xa!==e)if(e={context:e,memoizedValue:t,next:null},null===ka){if(null===va)throw Error(a(308));ka=e,va.dependencies={lanes:0,firstContext:e}}else ka=ka.next=e;return t}var _a=null;function Na(e){null===_a?_a=[e]:_a.push(e)}function Pa(e,t,n,r){var o=t.interleaved;return null===o?(n.next=n,Na(t)):(n.next=o.next,o.next=n),t.interleaved=n,Ta(e,r)}function Ta(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var La=!1;function za(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Da(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ra(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ma(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ps)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Ta(e,n)}return null===(o=r.interleaved)?(t.next=t,Na(r)):(t.next=o.next,o.next=t),r.interleaved=t,Ta(e,n)}function Oa(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Ia(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Fa(e,t,n,r){var o=e.updateQueue;La=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var s=l,u=s.next;s.next=null,null===i?a=u:i.next=u,i=s;var c=e.alternate;null!==c&&((l=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===l?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=s))}if(null!==a){var d=o.baseState;for(i=0,c=u=s=null,l=a;;){var f=l.lane,p=l.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var h=e,m=l;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=I({},d,f);break e;case 2:La=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(f=o.effects)?o.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,i|=f;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(f=l).next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}if(null===c&&(s=d),o.baseState=s,o.firstBaseUpdate=u,o.lastBaseUpdate=c,null!==(t=o.shared.interleaved)){o=t;do{i|=o.lane,o=o.next}while(o!==t)}else null===a&&(o.shared.lanes=0);Is|=i,e.lanes=i,e.memoizedState=d}}function $a(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!==typeof o)throw Error(a(191,o));o.call(r)}}}var Ua=(new r.Component).refs;function Aa(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:I({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var Ba={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=tu(),o=nu(e),a=Ra(r,o);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Ma(e,a,o))&&(ru(t,e,o,r),Oa(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=tu(),o=nu(e),a=Ra(r,o);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Ma(e,a,o))&&(ru(t,e,o,r),Oa(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=tu(),r=nu(e),o=Ra(n,r);o.tag=2,void 0!==t&&null!==t&&(o.callback=t),null!==(t=Ma(e,o,r))&&(ru(t,e,r,n),Oa(t,e,r))}};function Ha(e,t,n,r,o,a,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(o,a))}function Wa(e,t,n){var r=!1,o=No,a=t.contextType;return"object"===typeof a&&null!==a?a=Ca(a):(o=Do(t)?Lo:Po.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?zo(e,o):No),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=Ba,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=a),t}function Va(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ba.enqueueReplaceState(t,t.state,null)}function Ga(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=Ua,za(e);var a=t.contextType;"object"===typeof a&&null!==a?o.context=Ca(a):(a=Do(t)?Lo:Po.current,o.context=zo(e,a)),o.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(Aa(e,t,a,n),o.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(t=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&Ba.enqueueReplaceState(o,o.state,null),Fa(e,n,o,r),o.state=e.memoizedState),"function"===typeof o.componentDidMount&&(e.flags|=4194308)}function qa(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var o=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=o.refs;t===Ua&&(t=o.refs={}),null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function Qa(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ya(e){return(0,e._init)(e._payload)}function Xa(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=Ru(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Fu(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function u(e,t,n,r){var a=n.type;return a===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===z&&Ya(a)===t.type)?((r=o(t,n.props)).ref=qa(e,t,n),r.return=e,r):((r=Mu(n.type,n.key,n.props,null,e.mode,r)).ref=qa(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=$u(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Ou(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Fu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case x:return(n=Mu(t.type,t.key,t.props,null,e.mode,n)).ref=qa(e,null,t),n.return=e,n;case w:return(t=$u(t,e.mode,n)).return=e,t;case z:return f(e,(0,t._init)(t._payload),n)}if(te(t)||M(t))return(t=Ou(t,e.mode,n,null)).return=e,t;Qa(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case x:return n.key===o?u(e,t,n,r):null;case w:return n.key===o?c(e,t,n,r):null;case z:return p(e,t,(o=n._init)(n._payload),r)}if(te(n)||M(n))return null!==o?null:d(e,t,n,r,null);Qa(e,n)}return null}function h(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case x:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case z:return h(e,t,n,(0,r._init)(r._payload),o)}if(te(r)||M(r))return d(t,e=e.get(n)||null,r,o,null);Qa(t,r)}return null}function m(o,a,l,s){for(var u=null,c=null,d=a,m=a=0,g=null;null!==d&&m<l.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var b=p(o,d,l[m],s);if(null===b){null===d&&(d=g);break}e&&d&&null===b.alternate&&t(o,d),a=i(b,a,m),null===c?u=b:c.sibling=b,c=b,d=g}if(m===l.length)return n(o,d),aa&&Zo(o,m),u;if(null===d){for(;m<l.length;m++)null!==(d=f(o,l[m],s))&&(a=i(d,a,m),null===c?u=d:c.sibling=d,c=d);return aa&&Zo(o,m),u}for(d=r(o,d);m<l.length;m++)null!==(g=h(d,o,m,l[m],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),a=i(g,a,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach((function(e){return t(o,e)})),aa&&Zo(o,m),u}function g(o,l,s,u){var c=M(s);if("function"!==typeof c)throw Error(a(150));if(null==(s=c.call(s)))throw Error(a(151));for(var d=c=null,m=l,g=l=0,b=null,y=s.next();null!==m&&!y.done;g++,y=s.next()){m.index>g?(b=m,m=null):b=m.sibling;var v=p(o,m,y.value,u);if(null===v){null===m&&(m=b);break}e&&m&&null===v.alternate&&t(o,m),l=i(v,l,g),null===d?c=v:d.sibling=v,d=v,m=b}if(y.done)return n(o,m),aa&&Zo(o,g),c;if(null===m){for(;!y.done;g++,y=s.next())null!==(y=f(o,y.value,u))&&(l=i(y,l,g),null===d?c=y:d.sibling=y,d=y);return aa&&Zo(o,g),c}for(m=r(o,m);!y.done;g++,y=s.next())null!==(y=h(m,o,g,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),l=i(y,l,g),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach((function(e){return t(o,e)})),aa&&Zo(o,g),c}return function e(r,a,i,s){if("object"===typeof i&&null!==i&&i.type===S&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case x:e:{for(var u=i.key,c=a;null!==c;){if(c.key===u){if((u=i.type)===S){if(7===c.tag){n(r,c.sibling),(a=o(c,i.props.children)).return=r,r=a;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===z&&Ya(u)===c.type){n(r,c.sibling),(a=o(c,i.props)).ref=qa(r,c,i),a.return=r,r=a;break e}n(r,c);break}t(r,c),c=c.sibling}i.type===S?((a=Ou(i.props.children,r.mode,s,i.key)).return=r,r=a):((s=Mu(i.type,i.key,i.props,null,r.mode,s)).ref=qa(r,a,i),s.return=r,r=s)}return l(r);case w:e:{for(c=i.key;null!==a;){if(a.key===c){if(4===a.tag&&a.stateNode.containerInfo===i.containerInfo&&a.stateNode.implementation===i.implementation){n(r,a.sibling),(a=o(a,i.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=$u(i,r.mode,s)).return=r,r=a}return l(r);case z:return e(r,a,(c=i._init)(i._payload),s)}if(te(i))return m(r,a,i,s);if(M(i))return g(r,a,i,s);Qa(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==a&&6===a.tag?(n(r,a.sibling),(a=o(a,i)).return=r,r=a):(n(r,a),(a=Fu(i,r.mode,s)).return=r,r=a),l(r)):n(r,a)}}var Ka=Xa(!0),Ja=Xa(!1),Za={},ei=Eo(Za),ti=Eo(Za),ni=Eo(Za);function ri(e){if(e===Za)throw Error(a(174));return e}function oi(e,t){switch(_o(ni,t),_o(ti,e),_o(ei,Za),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Co(ei),_o(ei,t)}function ai(){Co(ei),Co(ti),Co(ni)}function ii(e){ri(ni.current);var t=ri(ei.current),n=se(t,e.type);t!==n&&(_o(ti,e),_o(ei,n))}function li(e){ti.current===e&&(Co(ei),Co(ti))}var si=Eo(0);function ui(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ci=[];function di(){for(var e=0;e<ci.length;e++)ci[e]._workInProgressVersionPrimary=null;ci.length=0}var fi=k.ReactCurrentDispatcher,pi=k.ReactCurrentBatchConfig,hi=0,mi=null,gi=null,bi=null,yi=!1,vi=!1,ki=0,xi=0;function wi(){throw Error(a(321))}function Si(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function ji(e,t,n,r,o,i){if(hi=i,mi=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,fi.current=null===e||null===e.memoizedState?ll:sl,e=n(r,o),vi){i=0;do{if(vi=!1,ki=0,25<=i)throw Error(a(301));i+=1,bi=gi=null,t.updateQueue=null,fi.current=ul,e=n(r,o)}while(vi)}if(fi.current=il,t=null!==gi&&null!==gi.next,hi=0,bi=gi=mi=null,yi=!1,t)throw Error(a(300));return e}function Ei(){var e=0!==ki;return ki=0,e}function Ci(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===bi?mi.memoizedState=bi=e:bi=bi.next=e,bi}function _i(){if(null===gi){var e=mi.alternate;e=null!==e?e.memoizedState:null}else e=gi.next;var t=null===bi?mi.memoizedState:bi.next;if(null!==t)bi=t,gi=e;else{if(null===e)throw Error(a(310));e={memoizedState:(gi=e).memoizedState,baseState:gi.baseState,baseQueue:gi.baseQueue,queue:gi.queue,next:null},null===bi?mi.memoizedState=bi=e:bi=bi.next=e}return bi}function Ni(e,t){return"function"===typeof t?t(e):t}function Pi(e){var t=_i(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=gi,o=r.baseQueue,i=n.pending;if(null!==i){if(null!==o){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(null!==o){i=o.next,r=r.baseState;var s=l=null,u=null,c=i;do{var d=c.lane;if((hi&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,l=r):u=u.next=f,mi.lanes|=d,Is|=d}c=c.next}while(null!==c&&c!==i);null===u?l=r:u.next=s,lr(r,t.memoizedState)||(kl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){o=e;do{i=o.lane,mi.lanes|=i,Is|=i,o=o.next}while(o!==e)}else null===o&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ti(e){var t=_i(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(null!==o){n.pending=null;var l=o=o.next;do{i=e(i,l.action),l=l.next}while(l!==o);lr(i,t.memoizedState)||(kl=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Li(){}function zi(e,t){var n=mi,r=_i(),o=t(),i=!lr(r.memoizedState,o);if(i&&(r.memoizedState=o,kl=!0),r=r.queue,Wi(Mi.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==bi&&1&bi.memoizedState.tag){if(n.flags|=2048,$i(9,Ri.bind(null,n,r,o,t),void 0,null),null===Ts)throw Error(a(349));0!==(30&hi)||Di(n,t,o)}return o}function Di(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=mi.updateQueue)?(t={lastEffect:null,stores:null},mi.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ri(e,t,n,r){t.value=n,t.getSnapshot=r,Oi(t)&&Ii(e)}function Mi(e,t,n){return n((function(){Oi(t)&&Ii(e)}))}function Oi(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lr(e,n)}catch(r){return!0}}function Ii(e){var t=Ta(e,1);null!==t&&ru(t,e,1,-1)}function Fi(e){var t=Ci();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ni,lastRenderedState:e},t.queue=e,e=e.dispatch=nl.bind(null,mi,e),[t.memoizedState,e]}function $i(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=mi.updateQueue)?(t={lastEffect:null,stores:null},mi.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ui(){return _i().memoizedState}function Ai(e,t,n,r){var o=Ci();mi.flags|=e,o.memoizedState=$i(1|t,n,void 0,void 0===r?null:r)}function Bi(e,t,n,r){var o=_i();r=void 0===r?null:r;var a=void 0;if(null!==gi){var i=gi.memoizedState;if(a=i.destroy,null!==r&&Si(r,i.deps))return void(o.memoizedState=$i(t,n,a,r))}mi.flags|=e,o.memoizedState=$i(1|t,n,a,r)}function Hi(e,t){return Ai(8390656,8,e,t)}function Wi(e,t){return Bi(2048,8,e,t)}function Vi(e,t){return Bi(4,2,e,t)}function Gi(e,t){return Bi(4,4,e,t)}function qi(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Qi(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Bi(4,4,qi.bind(null,t,e),n)}function Yi(){}function Xi(e,t){var n=_i();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Si(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ki(e,t){var n=_i();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Si(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ji(e,t,n){return 0===(21&hi)?(e.baseState&&(e.baseState=!1,kl=!0),e.memoizedState=n):(lr(n,t)||(n=mt(),mi.lanes|=n,Is|=n,e.baseState=!0),t)}function Zi(e,t){var n=vt;vt=0!==n&&4>n?n:4,e(!0);var r=pi.transition;pi.transition={};try{e(!1),t()}finally{vt=n,pi.transition=r}}function el(){return _i().memoizedState}function tl(e,t,n){var r=nu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},rl(e))ol(t,n);else if(null!==(n=Pa(e,t,n,r))){ru(n,e,r,tu()),al(n,t,r)}}function nl(e,t,n){var r=nu(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(rl(e))ol(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=a(i,n);if(o.hasEagerState=!0,o.eagerState=l,lr(l,i)){var s=t.interleaved;return null===s?(o.next=o,Na(t)):(o.next=s.next,s.next=o),void(t.interleaved=o)}}catch(u){}null!==(n=Pa(e,t,o,r))&&(ru(n,e,r,o=tu()),al(n,t,r))}}function rl(e){var t=e.alternate;return e===mi||null!==t&&t===mi}function ol(e,t){vi=yi=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function al(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var il={readContext:Ca,useCallback:wi,useContext:wi,useEffect:wi,useImperativeHandle:wi,useInsertionEffect:wi,useLayoutEffect:wi,useMemo:wi,useReducer:wi,useRef:wi,useState:wi,useDebugValue:wi,useDeferredValue:wi,useTransition:wi,useMutableSource:wi,useSyncExternalStore:wi,useId:wi,unstable_isNewReconciler:!1},ll={readContext:Ca,useCallback:function(e,t){return Ci().memoizedState=[e,void 0===t?null:t],e},useContext:Ca,useEffect:Hi,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ai(4194308,4,qi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ai(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ai(4,2,e,t)},useMemo:function(e,t){var n=Ci();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ci();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=tl.bind(null,mi,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ci().memoizedState=e},useState:Fi,useDebugValue:Yi,useDeferredValue:function(e){return Ci().memoizedState=e},useTransition:function(){var e=Fi(!1),t=e[0];return e=Zi.bind(null,e[1]),Ci().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=mi,o=Ci();if(aa){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===Ts)throw Error(a(349));0!==(30&hi)||Di(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Hi(Mi.bind(null,r,i,e),[e]),r.flags|=2048,$i(9,Ri.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Ci(),t=Ts.identifierPrefix;if(aa){var n=Jo;t=":"+t+"R"+(n=(Ko&~(1<<32-it(Ko)-1)).toString(32)+n),0<(n=ki++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=xi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},sl={readContext:Ca,useCallback:Xi,useContext:Ca,useEffect:Wi,useImperativeHandle:Qi,useInsertionEffect:Vi,useLayoutEffect:Gi,useMemo:Ki,useReducer:Pi,useRef:Ui,useState:function(){return Pi(Ni)},useDebugValue:Yi,useDeferredValue:function(e){return Ji(_i(),gi.memoizedState,e)},useTransition:function(){return[Pi(Ni)[0],_i().memoizedState]},useMutableSource:Li,useSyncExternalStore:zi,useId:el,unstable_isNewReconciler:!1},ul={readContext:Ca,useCallback:Xi,useContext:Ca,useEffect:Wi,useImperativeHandle:Qi,useInsertionEffect:Vi,useLayoutEffect:Gi,useMemo:Ki,useReducer:Ti,useRef:Ui,useState:function(){return Ti(Ni)},useDebugValue:Yi,useDeferredValue:function(e){var t=_i();return null===gi?t.memoizedState=e:Ji(t,gi.memoizedState,e)},useTransition:function(){return[Ti(Ni)[0],_i().memoizedState]},useMutableSource:Li,useSyncExternalStore:zi,useId:el,unstable_isNewReconciler:!1};function cl(e,t){try{var n="",r=t;do{n+=A(r),r=r.return}while(r);var o=n}catch(a){o="\nError generating stack: "+a.message+"\n"+a.stack}return{value:e,source:t,stack:o,digest:null}}function dl(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function fl(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var pl="function"===typeof WeakMap?WeakMap:Map;function hl(e,t,n){(n=Ra(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vs||(Vs=!0,Gs=r),fl(0,t)},n}function ml(e,t,n){(n=Ra(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){fl(0,t)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){fl(0,t),"function"!==typeof r&&(null===qs?qs=new Set([this]):qs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function gl(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new pl;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Cu.bind(null,e,t,n),t.then(e,e))}function bl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yl(e,t,n,r,o){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ra(-1,1)).tag=2,Ma(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var vl=k.ReactCurrentOwner,kl=!1;function xl(e,t,n,r){t.child=null===e?Ja(t,null,n,r):Ka(t,e.child,n,r)}function wl(e,t,n,r,o){n=n.render;var a=t.ref;return Ea(t,o),r=ji(e,t,n,r,a,o),n=Ei(),null===e||kl?(aa&&n&&ta(t),t.flags|=1,xl(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Vl(e,t,o))}function Sl(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||Du(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Mu(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,jl(e,t,a,r,o))}if(a=e.child,0===(e.lanes&o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(i,r)&&e.ref===t.ref)return Vl(e,t,o)}return t.flags|=1,(e=Ru(a,r)).ref=t.ref,e.return=t,t.child=e}function jl(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(sr(a,r)&&e.ref===t.ref){if(kl=!1,t.pendingProps=r=a,0===(e.lanes&o))return t.lanes=e.lanes,Vl(e,t,o);0!==(131072&e.flags)&&(kl=!0)}}return _l(e,t,n,r,o)}function El(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},_o(Rs,Ds),Ds|=n;else{if(0===(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,_o(Rs,Ds),Ds|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,_o(Rs,Ds),Ds|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,_o(Rs,Ds),Ds|=r;return xl(e,t,o,n),t.child}function Cl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function _l(e,t,n,r,o){var a=Do(n)?Lo:Po.current;return a=zo(t,a),Ea(t,o),n=ji(e,t,n,r,a,o),r=Ei(),null===e||kl?(aa&&r&&ta(t),t.flags|=1,xl(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Vl(e,t,o))}function Nl(e,t,n,r,o){if(Do(n)){var a=!0;Io(t)}else a=!1;if(Ea(t,o),null===t.stateNode)Wl(e,t),Wa(t,n,r),Ga(t,n,r,o),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var s=i.context,u=n.contextType;"object"===typeof u&&null!==u?u=Ca(u):u=zo(t,u=Do(n)?Lo:Po.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==r||s!==u)&&Va(t,i,r,u),La=!1;var f=t.memoizedState;i.state=f,Fa(t,r,i,o),s=t.memoizedState,l!==r||f!==s||To.current||La?("function"===typeof c&&(Aa(t,n,c,r),s=t.memoizedState),(l=La||Ha(t,n,l,r,f,s,u))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=u,r=l):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Da(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:ba(t.type,l),i.props=u,d=t.pendingProps,f=i.context,"object"===typeof(s=n.contextType)&&null!==s?s=Ca(s):s=zo(t,s=Do(n)?Lo:Po.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==d||f!==s)&&Va(t,i,r,s),La=!1,f=t.memoizedState,i.state=f,Fa(t,r,i,o);var h=t.memoizedState;l!==d||f!==h||To.current||La?("function"===typeof p&&(Aa(t,n,p,r),h=t.memoizedState),(u=La||Ha(t,n,u,r,f,h,s)||!1)?(c||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,s),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=s,r=u):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Pl(e,t,n,r,a,o)}function Pl(e,t,n,r,o,a){Cl(e,t);var i=0!==(128&t.flags);if(!r&&!i)return o&&Fo(t,n,!1),Vl(e,t,a);r=t.stateNode,vl.current=t;var l=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=Ka(t,e.child,null,a),t.child=Ka(t,null,l,a)):xl(e,t,l,a),t.memoizedState=r.state,o&&Fo(t,n,!0),t.child}function Tl(e){var t=e.stateNode;t.pendingContext?Mo(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Mo(0,t.context,!1),oi(e,t.containerInfo)}function Ll(e,t,n,r,o){return ha(),ma(o),t.flags|=256,xl(e,t,n,r),t.child}var zl,Dl,Rl,Ml,Ol={dehydrated:null,treeContext:null,retryLane:0};function Il(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fl(e,t,n){var r,o=t.pendingProps,i=si.current,l=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),_o(si,1&i),null===e)return ca(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=o.children,e=o.fallback,l?(o=t.mode,l=t.child,s={mode:"hidden",children:s},0===(1&o)&&null!==l?(l.childLanes=0,l.pendingProps=s):l=Iu(s,o,0,null),e=Ou(e,o,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Il(n),t.memoizedState=Ol,e):$l(t,s));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,o,i,l){if(n)return 256&t.flags?(t.flags&=-257,Ul(e,t,l,r=dl(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Iu({mode:"visible",children:r.children},o,0,null),(i=Ou(i,o,l,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&Ka(t,e.child,null,l),t.child.memoizedState=Il(l),t.memoizedState=Ol,i);if(0===(1&t.mode))return Ul(e,t,l,null);if("$!"===o.data){if(r=o.nextSibling&&o.nextSibling.dataset)var s=r.dgst;return r=s,Ul(e,t,l,r=dl(i=Error(a(419)),r,void 0))}if(s=0!==(l&e.childLanes),kl||s){if(null!==(r=Ts)){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!==(o&(r.suspendedLanes|l))?0:o)&&o!==i.retryLane&&(i.retryLane=o,Ta(e,o),ru(r,e,o,-1))}return gu(),Ul(e,t,l,r=dl(Error(a(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=Nu.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,oa=uo(o.nextSibling),ra=t,aa=!0,ia=null,null!==e&&(Qo[Yo++]=Ko,Qo[Yo++]=Jo,Qo[Yo++]=Xo,Ko=e.id,Jo=e.overflow,Xo=t),t=$l(t,r.children),t.flags|=4096,t)}(e,t,s,o,r,i,n);if(l){l=o.fallback,s=t.mode,r=(i=e.child).sibling;var u={mode:"hidden",children:o.children};return 0===(1&s)&&t.child!==i?((o=t.child).childLanes=0,o.pendingProps=u,t.deletions=null):(o=Ru(i,u)).subtreeFlags=14680064&i.subtreeFlags,null!==r?l=Ru(r,l):(l=Ou(l,s,n,null)).flags|=2,l.return=t,o.return=t,o.sibling=l,t.child=o,o=l,l=t.child,s=null===(s=e.child.memoizedState)?Il(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=Ol,o}return e=(l=e.child).sibling,o=Ru(l,{mode:"visible",children:o.children}),0===(1&t.mode)&&(o.lanes=n),o.return=t,o.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function $l(e,t){return(t=Iu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ul(e,t,n,r){return null!==r&&ma(r),Ka(t,e.child,null,n),(e=$l(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Al(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ja(e.return,t,n)}function Bl(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Hl(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(xl(e,t,r.children,n),0!==(2&(r=si.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Al(e,n,t);else if(19===e.tag)Al(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(_o(si,r),0===(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ui(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Bl(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ui(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Bl(t,!0,n,null,a);break;case"together":Bl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wl(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Is|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Ru(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ru(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Gl(e,t){if(!aa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ql(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=14680064&o.subtreeFlags,r|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ql(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ql(t),null;case 1:case 17:return Do(t.type)&&Ro(),ql(t),null;case 3:return r=t.stateNode,ai(),Co(To),Co(Po),di(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fa(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ia&&(lu(ia),ia=null))),Dl(e,t),ql(t),null;case 5:li(t);var o=ri(ni.current);if(n=t.type,null!==e&&null!=t.stateNode)Rl(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return ql(t),null}if(e=ri(ei.current),fa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[po]=t,r[ho]=i,e=0!==(1&t.mode),n){case"dialog":$r("cancel",r),$r("close",r);break;case"iframe":case"object":case"embed":$r("load",r);break;case"video":case"audio":for(o=0;o<Mr.length;o++)$r(Mr[o],r);break;case"source":$r("error",r);break;case"img":case"image":case"link":$r("error",r),$r("load",r);break;case"details":$r("toggle",r);break;case"input":X(r,i),$r("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},$r("invalid",r);break;case"textarea":oe(r,i),$r("invalid",r)}for(var s in ye(n,i),o=null,i)if(i.hasOwnProperty(s)){var u=i[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==i.suppressHydrationWarning&&Jr(r.textContent,u,e),o=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==i.suppressHydrationWarning&&Jr(r.textContent,u,e),o=["children",""+u]):l.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&$r("scroll",r)}switch(n){case"input":G(r),Z(r,i,!0);break;case"textarea":G(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Zr)}r=o,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[po]=t,e[ho]=r,zl(e,t,!1,!1),t.stateNode=e;e:{switch(s=ve(n,r),n){case"dialog":$r("cancel",e),$r("close",e),o=r;break;case"iframe":case"object":case"embed":$r("load",e),o=r;break;case"video":case"audio":for(o=0;o<Mr.length;o++)$r(Mr[o],e);o=r;break;case"source":$r("error",e),o=r;break;case"img":case"image":case"link":$r("error",e),$r("load",e),o=r;break;case"details":$r("toggle",e),o=r;break;case"input":X(e,r),o=Y(e,r),$r("invalid",e);break;case"option":default:o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=I({},r,{value:void 0}),$r("invalid",e);break;case"textarea":oe(e,r),o=re(e,r),$r("invalid",e)}for(i in ye(n,o),u=o)if(u.hasOwnProperty(i)){var c=u[i];"style"===i?ge(e,c):"dangerouslySetInnerHTML"===i?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===i?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(l.hasOwnProperty(i)?null!=c&&"onScroll"===i&&$r("scroll",e):null!=c&&v(e,i,c,s))}switch(n){case"input":G(e),Z(e,r,!1);break;case"textarea":G(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+W(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof o.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return ql(t),null;case 6:if(e&&null!=t.stateNode)Ml(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));if(n=ri(ni.current),ri(ei.current),fa(t)){if(r=t.stateNode,n=t.memoizedProps,r[po]=t,(i=r.nodeValue!==n)&&null!==(e=ra))switch(e.tag){case 3:Jr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[po]=t,t.stateNode=r}return ql(t),null;case 13:if(Co(si),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(aa&&null!==oa&&0!==(1&t.mode)&&0===(128&t.flags))pa(),ha(),t.flags|=98560,i=!1;else if(i=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(a(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(a(317));i[po]=t}else ha(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ql(t),i=!1}else null!==ia&&(lu(ia),ia=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&si.current)?0===Ms&&(Ms=3):gu())),null!==t.updateQueue&&(t.flags|=4),ql(t),null);case 4:return ai(),Dl(e,t),null===e&&Br(t.stateNode.containerInfo),ql(t),null;case 10:return Sa(t.type._context),ql(t),null;case 19:if(Co(si),null===(i=t.memoizedState))return ql(t),null;if(r=0!==(128&t.flags),null===(s=i.rendering))if(r)Gl(i,!1);else{if(0!==Ms||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=ui(e))){for(t.flags|=128,Gl(i,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(s=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return _o(si,1&si.current|2),t.child}e=e.sibling}null!==i.tail&&Ke()>Hs&&(t.flags|=128,r=!0,Gl(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ui(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Gl(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!aa)return ql(t),null}else 2*Ke()-i.renderingStartTime>Hs&&1073741824!==n&&(t.flags|=128,r=!0,Gl(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=i.last)?n.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ke(),t.sibling=null,n=si.current,_o(si,r?1&n|2:1&n),t):(ql(t),null);case 22:case 23:return fu(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ds)&&(ql(t),6&t.subtreeFlags&&(t.flags|=8192)):ql(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function Yl(e,t){switch(na(t),t.tag){case 1:return Do(t.type)&&Ro(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ai(),Co(To),Co(Po),di(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return li(t),null;case 13:if(Co(si),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ha()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Co(si),null;case 4:return ai(),null;case 10:return Sa(t.type._context),null;case 22:case 23:return fu(),null;default:return null}}zl=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Dl=function(){},Rl=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,ri(ei.current);var a,i=null;switch(n){case"input":o=Y(e,o),r=Y(e,r),i=[];break;case"select":o=I({},o,{value:void 0}),r=I({},r,{value:void 0}),i=[];break;case"textarea":o=re(e,o),r=re(e,r),i=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ye(n,r),n=null,o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&null!=o[c])if("style"===c){var s=o[c];for(a in s)s.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(l.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=o?o[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(a in s)!s.hasOwnProperty(a)||u&&u.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in u)u.hasOwnProperty(a)&&s[a]!==u[a]&&(n||(n={}),n[a]=u[a])}else n||(i||(i=[]),i.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(i=i||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(i=i||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(l.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&$r("scroll",e),i||s===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}},Ml=function(e,t,n,r){n!==r&&(t.flags|=4)};var Xl=!1,Kl=!1,Jl="function"===typeof WeakSet?WeakSet:Set,Zl=null;function es(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Eu(e,t,r)}else n.current=null}function ts(e,t,n){try{n()}catch(r){Eu(e,t,r)}}var ns=!1;function rs(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var o=r=r.next;do{if((o.tag&e)===e){var a=o.destroy;o.destroy=void 0,void 0!==a&&ts(t,n,a)}o=o.next}while(o!==r)}}function os(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function is(e){var t=e.alternate;null!==t&&(e.alternate=null,is(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[po],delete t[ho],delete t[go],delete t[bo],delete t[yo])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ls(e){return 5===e.tag||3===e.tag||4===e.tag}function ss(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ls(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}function cs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cs(e,t,n),e=e.sibling;null!==e;)cs(e,t,n),e=e.sibling}var ds=null,fs=!1;function ps(e,t,n){for(n=n.child;null!==n;)hs(e,t,n),n=n.sibling}function hs(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(ot,n)}catch(l){}switch(n.tag){case 5:Kl||es(n,t);case 6:var r=ds,o=fs;ds=null,ps(e,t,n),fs=o,null!==(ds=r)&&(fs?(e=ds,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ds.removeChild(n.stateNode));break;case 18:null!==ds&&(fs?(e=ds,n=n.stateNode,8===e.nodeType?so(e.parentNode,n):1===e.nodeType&&so(e,n),Bt(e)):so(ds,n.stateNode));break;case 4:r=ds,o=fs,ds=n.stateNode.containerInfo,fs=!0,ps(e,t,n),ds=r,fs=o;break;case 0:case 11:case 14:case 15:if(!Kl&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){o=r=r.next;do{var a=o,i=a.destroy;a=a.tag,void 0!==i&&(0!==(2&a)||0!==(4&a))&&ts(n,t,i),o=o.next}while(o!==r)}ps(e,t,n);break;case 1:if(!Kl&&(es(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Eu(n,t,l)}ps(e,t,n);break;case 21:ps(e,t,n);break;case 22:1&n.mode?(Kl=(r=Kl)||null!==n.memoizedState,ps(e,t,n),Kl=r):ps(e,t,n);break;default:ps(e,t,n)}}function ms(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Jl),t.forEach((function(t){var r=Pu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function gs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:ds=s.stateNode,fs=!1;break e;case 3:case 4:ds=s.stateNode.containerInfo,fs=!0;break e}s=s.return}if(null===ds)throw Error(a(160));hs(i,l,o),ds=null,fs=!1;var u=o.alternate;null!==u&&(u.return=null),o.return=null}catch(c){Eu(o,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)bs(t,e),t=t.sibling}function bs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gs(t,e),ys(e),4&r){try{rs(3,e,e.return),os(3,e)}catch(g){Eu(e,e.return,g)}try{rs(5,e,e.return)}catch(g){Eu(e,e.return,g)}}break;case 1:gs(t,e),ys(e),512&r&&null!==n&&es(n,n.return);break;case 5:if(gs(t,e),ys(e),512&r&&null!==n&&es(n,n.return),32&e.flags){var o=e.stateNode;try{fe(o,"")}catch(g){Eu(e,e.return,g)}}if(4&r&&null!=(o=e.stateNode)){var i=e.memoizedProps,l=null!==n?n.memoizedProps:i,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===i.type&&null!=i.name&&K(o,i),ve(s,l);var c=ve(s,i);for(l=0;l<u.length;l+=2){var d=u[l],f=u[l+1];"style"===d?ge(o,f):"dangerouslySetInnerHTML"===d?de(o,f):"children"===d?fe(o,f):v(o,d,f,c)}switch(s){case"input":J(o,i);break;case"textarea":ae(o,i);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;null!=h?ne(o,!!i.multiple,h,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(o,!!i.multiple,i.defaultValue,!0):ne(o,!!i.multiple,i.multiple?[]:"",!1))}o[ho]=i}catch(g){Eu(e,e.return,g)}}break;case 6:if(gs(t,e),ys(e),4&r){if(null===e.stateNode)throw Error(a(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(g){Eu(e,e.return,g)}}break;case 3:if(gs(t,e),ys(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(g){Eu(e,e.return,g)}break;case 4:default:gs(t,e),ys(e);break;case 13:gs(t,e),ys(e),8192&(o=e.child).flags&&(i=null!==o.memoizedState,o.stateNode.isHidden=i,!i||null!==o.alternate&&null!==o.alternate.memoizedState||(Bs=Ke())),4&r&&ms(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Kl=(c=Kl)||d,gs(t,e),Kl=c):gs(t,e),ys(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Zl=e,d=e.child;null!==d;){for(f=Zl=d;null!==Zl;){switch(h=(p=Zl).child,p.tag){case 0:case 11:case 14:case 15:rs(4,p,p.return);break;case 1:es(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Eu(r,n,g)}}break;case 5:es(p,p.return);break;case 22:if(null!==p.memoizedState){ws(f);continue}}null!==h?(h.return=p,Zl=h):ws(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{o=f.stateNode,c?"function"===typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none":(s=f.stateNode,l=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=me("display",l))}catch(g){Eu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Eu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:gs(t,e),ys(e),4&r&&ms(e);case 21:}}function ys(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ls(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var o=r.stateNode;32&r.flags&&(fe(o,""),r.flags&=-33),cs(e,ss(e),o);break;case 3:case 4:var i=r.stateNode.containerInfo;us(e,ss(e),i);break;default:throw Error(a(161))}}catch(l){Eu(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vs(e,t,n){Zl=e,ks(e,t,n)}function ks(e,t,n){for(var r=0!==(1&e.mode);null!==Zl;){var o=Zl,a=o.child;if(22===o.tag&&r){var i=null!==o.memoizedState||Xl;if(!i){var l=o.alternate,s=null!==l&&null!==l.memoizedState||Kl;l=Xl;var u=Kl;if(Xl=i,(Kl=s)&&!u)for(Zl=o;null!==Zl;)s=(i=Zl).child,22===i.tag&&null!==i.memoizedState?Ss(o):null!==s?(s.return=i,Zl=s):Ss(o);for(;null!==a;)Zl=a,ks(a,t,n),a=a.sibling;Zl=o,Xl=l,Kl=u}xs(e)}else 0!==(8772&o.subtreeFlags)&&null!==a?(a.return=o,Zl=a):xs(e)}}function xs(e){for(;null!==Zl;){var t=Zl;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Kl||os(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Kl)if(null===n)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ba(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&$a(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}$a(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Bt(f)}}}break;default:throw Error(a(163))}Kl||512&t.flags&&as(t)}catch(p){Eu(t,t.return,p)}}if(t===e){Zl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zl=n;break}Zl=t.return}}function ws(e){for(;null!==Zl;){var t=Zl;if(t===e){Zl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zl=n;break}Zl=t.return}}function Ss(e){for(;null!==Zl;){var t=Zl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{os(4,t)}catch(s){Eu(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var o=t.return;try{r.componentDidMount()}catch(s){Eu(t,o,s)}}var a=t.return;try{as(t)}catch(s){Eu(t,a,s)}break;case 5:var i=t.return;try{as(t)}catch(s){Eu(t,i,s)}}}catch(s){Eu(t,t.return,s)}if(t===e){Zl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Zl=l;break}Zl=t.return}}var js,Es=Math.ceil,Cs=k.ReactCurrentDispatcher,_s=k.ReactCurrentOwner,Ns=k.ReactCurrentBatchConfig,Ps=0,Ts=null,Ls=null,zs=0,Ds=0,Rs=Eo(0),Ms=0,Os=null,Is=0,Fs=0,$s=0,Us=null,As=null,Bs=0,Hs=1/0,Ws=null,Vs=!1,Gs=null,qs=null,Qs=!1,Ys=null,Xs=0,Ks=0,Js=null,Zs=-1,eu=0;function tu(){return 0!==(6&Ps)?Ke():-1!==Zs?Zs:Zs=Ke()}function nu(e){return 0===(1&e.mode)?1:0!==(2&Ps)&&0!==zs?zs&-zs:null!==ga.transition?(0===eu&&(eu=mt()),eu):0!==(e=vt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function ru(e,t,n,r){if(50<Ks)throw Ks=0,Js=null,Error(a(185));bt(e,n,r),0!==(2&Ps)&&e===Ts||(e===Ts&&(0===(2&Ps)&&(Fs|=n),4===Ms&&su(e,zs)),ou(e,r),1===n&&0===Ps&&0===(1&t.mode)&&(Hs=Ke()+500,Uo&&Ho()))}function ou(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-it(a),l=1<<i,s=o[i];-1===s?0!==(l&n)&&0===(l&r)||(o[i]=pt(l,t)):s<=t&&(e.expiredLanes|=l),a&=~l}}(e,t);var r=ft(e,e===Ts?zs:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){Uo=!0,Bo(e)}(uu.bind(null,e)):Bo(uu.bind(null,e)),io((function(){0===(6&Ps)&&Ho()})),n=null;else{switch(kt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Tu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Zs=-1,eu=0,0!==(6&Ps))throw Error(a(327));var n=e.callbackNode;if(Su()&&e.callbackNode!==n)return null;var r=ft(e,e===Ts?zs:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=bu(e,r);else{t=r;var o=Ps;Ps|=2;var i=mu();for(Ts===e&&zs===t||(Ws=null,Hs=Ke()+500,pu(e,t));;)try{vu();break}catch(s){hu(e,s)}wa(),Cs.current=i,Ps=o,null!==Ls?t=0:(Ts=null,zs=0,t=Ms)}if(0!==t){if(2===t&&(0!==(o=ht(e))&&(r=o,t=iu(e,o))),1===t)throw n=Os,pu(e,0),su(e,r),ou(e,Ke()),n;if(6===t)su(e,r);else{if(o=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!lr(a(),o))return!1}catch(l){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=bu(e,r))&&(0!==(i=ht(e))&&(r=i,t=iu(e,i))),1===t))throw n=Os,pu(e,0),su(e,r),ou(e,Ke()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:wu(e,As,Ws);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=Bs+500-Ke())){if(0!==ft(e,0))break;if(((o=e.suspendedLanes)&r)!==r){tu(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ro(wu.bind(null,e,As,Ws),t);break}wu(e,As,Ws);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-it(r);i=1<<l,(l=t[l])>o&&(o=l),r&=~i}if(r=o,10<(r=(120>(r=Ke()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Es(r/1960))-r)){e.timeoutHandle=ro(wu.bind(null,e,As,Ws),r);break}wu(e,As,Ws);break;default:throw Error(a(329))}}}return ou(e,Ke()),e.callbackNode===n?au.bind(null,e):null}function iu(e,t){var n=Us;return e.current.memoizedState.isDehydrated&&(pu(e,t).flags|=256),2!==(e=bu(e,t))&&(t=As,As=n,null!==t&&lu(t)),e}function lu(e){null===As?As=e:As.push.apply(As,e)}function su(e,t){for(t&=~$s,t&=~Fs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function uu(e){if(0!==(6&Ps))throw Error(a(327));Su();var t=ft(e,0);if(0===(1&t))return ou(e,Ke()),null;var n=bu(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=iu(e,r))}if(1===n)throw n=Os,pu(e,0),su(e,t),ou(e,Ke()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,As,Ws),ou(e,Ke()),null}function cu(e,t){var n=Ps;Ps|=1;try{return e(t)}finally{0===(Ps=n)&&(Hs=Ke()+500,Uo&&Ho())}}function du(e){null!==Ys&&0===Ys.tag&&0===(6&Ps)&&Su();var t=Ps;Ps|=1;var n=Ns.transition,r=vt;try{if(Ns.transition=null,vt=1,e)return e()}finally{vt=r,Ns.transition=n,0===(6&(Ps=t))&&Ho()}}function fu(){Ds=Rs.current,Co(Rs)}function pu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,oo(n)),null!==Ls)for(n=Ls.return;null!==n;){var r=n;switch(na(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ro();break;case 3:ai(),Co(To),Co(Po),di();break;case 5:li(r);break;case 4:ai();break;case 13:case 19:Co(si);break;case 10:Sa(r.type._context);break;case 22:case 23:fu()}n=n.return}if(Ts=e,Ls=e=Ru(e.current,null),zs=Ds=t,Ms=0,Os=null,$s=Fs=Is=0,As=Us=null,null!==_a){for(t=0;t<_a.length;t++)if(null!==(r=(n=_a[t]).interleaved)){n.interleaved=null;var o=r.next,a=n.pending;if(null!==a){var i=a.next;a.next=o,r.next=i}n.pending=r}_a=null}return e}function hu(e,t){for(;;){var n=Ls;try{if(wa(),fi.current=il,yi){for(var r=mi.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}yi=!1}if(hi=0,bi=gi=mi=null,vi=!1,ki=0,_s.current=null,null===n||null===n.return){Ms=1,Os=t,Ls=null;break}e:{var i=e,l=n.return,s=n,u=t;if(t=zs,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=bl(l);if(null!==h){h.flags&=-257,yl(h,l,s,0,t),1&h.mode&&gl(i,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){gl(i,c,t),gu();break e}u=Error(a(426))}else if(aa&&1&s.mode){var b=bl(l);if(null!==b){0===(65536&b.flags)&&(b.flags|=256),yl(b,l,s,0,t),ma(cl(u,s));break e}}i=u=cl(u,s),4!==Ms&&(Ms=2),null===Us?Us=[i]:Us.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Ia(i,hl(0,u,t));break e;case 1:s=u;var y=i.type,v=i.stateNode;if(0===(128&i.flags)&&("function"===typeof y.getDerivedStateFromError||null!==v&&"function"===typeof v.componentDidCatch&&(null===qs||!qs.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t,Ia(i,ml(i,s,t));break e}}i=i.return}while(null!==i)}xu(n)}catch(k){t=k,Ls===n&&null!==n&&(Ls=n=n.return);continue}break}}function mu(){var e=Cs.current;return Cs.current=il,null===e?il:e}function gu(){0!==Ms&&3!==Ms&&2!==Ms||(Ms=4),null===Ts||0===(268435455&Is)&&0===(268435455&Fs)||su(Ts,zs)}function bu(e,t){var n=Ps;Ps|=2;var r=mu();for(Ts===e&&zs===t||(Ws=null,pu(e,t));;)try{yu();break}catch(o){hu(e,o)}if(wa(),Ps=n,Cs.current=r,null!==Ls)throw Error(a(261));return Ts=null,zs=0,Ms}function yu(){for(;null!==Ls;)ku(Ls)}function vu(){for(;null!==Ls&&!Ye();)ku(Ls)}function ku(e){var t=js(e.alternate,e,Ds);e.memoizedProps=e.pendingProps,null===t?xu(e):Ls=t,_s.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Ql(n,t,Ds)))return void(Ls=n)}else{if(null!==(n=Yl(n,t)))return n.flags&=32767,void(Ls=n);if(null===e)return Ms=6,void(Ls=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ls=t);Ls=t=e}while(null!==t);0===Ms&&(Ms=5)}function wu(e,t,n){var r=vt,o=Ns.transition;try{Ns.transition=null,vt=1,function(e,t,n,r){do{Su()}while(null!==Ys);if(0!==(6&Ps))throw Error(a(327));n=e.finishedWork;var o=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-it(n),a=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~a}}(e,i),e===Ts&&(Ls=Ts=null,zs=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Qs||(Qs=!0,Tu(tt,(function(){return Su(),null}))),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=Ns.transition,Ns.transition=null;var l=vt;vt=1;var s=Ps;Ps|=4,_s.current=null,function(e,t){if(eo=Wt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(x){n=null;break e}var l=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==o&&3!==f.nodeType||(s=l+o),f!==i||0!==r&&3!==f.nodeType||(u=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===o&&(s=l),p===i&&++d===r&&(u=l),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(to={focusedElem:e,selectionRange:n},Wt=!1,Zl=t;null!==Zl;)if(e=(t=Zl).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zl=e;else for(;null!==Zl;){t=Zl;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,b=m.memoizedState,y=t.stateNode,v=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:ba(t.type,g),b);y.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var k=t.stateNode.containerInfo;1===k.nodeType?k.textContent="":9===k.nodeType&&k.documentElement&&k.removeChild(k.documentElement);break;default:throw Error(a(163))}}catch(x){Eu(t,t.return,x)}if(null!==(e=t.sibling)){e.return=t.return,Zl=e;break}Zl=t.return}m=ns,ns=!1}(e,n),bs(n,e),hr(to),Wt=!!eo,to=eo=null,e.current=n,vs(n,e,o),Xe(),Ps=s,vt=l,Ns.transition=i}else e.current=n;if(Qs&&(Qs=!1,Ys=e,Xs=o),i=e.pendingLanes,0===i&&(qs=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(ot,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ou(e,Ke()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Vs)throw Vs=!1,e=Gs,Gs=null,e;0!==(1&Xs)&&0!==e.tag&&Su(),i=e.pendingLanes,0!==(1&i)?e===Js?Ks++:(Ks=0,Js=e):Ks=0,Ho()}(e,t,n,r)}finally{Ns.transition=o,vt=r}return null}function Su(){if(null!==Ys){var e=kt(Xs),t=Ns.transition,n=vt;try{if(Ns.transition=null,vt=16>e?16:e,null===Ys)var r=!1;else{if(e=Ys,Ys=null,Xs=0,0!==(6&Ps))throw Error(a(331));var o=Ps;for(Ps|=4,Zl=e.current;null!==Zl;){var i=Zl,l=i.child;if(0!==(16&Zl.flags)){var s=i.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Zl=c;null!==Zl;){var d=Zl;switch(d.tag){case 0:case 11:case 15:rs(8,d,i)}var f=d.child;if(null!==f)f.return=d,Zl=f;else for(;null!==Zl;){var p=(d=Zl).sibling,h=d.return;if(is(d),d===c){Zl=null;break}if(null!==p){p.return=h,Zl=p;break}Zl=h}}}var m=i.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var b=g.sibling;g.sibling=null,g=b}while(null!==g)}}Zl=i}}if(0!==(2064&i.subtreeFlags)&&null!==l)l.return=i,Zl=l;else e:for(;null!==Zl;){if(0!==(2048&(i=Zl).flags))switch(i.tag){case 0:case 11:case 15:rs(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Zl=y;break e}Zl=i.return}}var v=e.current;for(Zl=v;null!==Zl;){var k=(l=Zl).child;if(0!==(2064&l.subtreeFlags)&&null!==k)k.return=l,Zl=k;else e:for(l=v;null!==Zl;){if(0!==(2048&(s=Zl).flags))try{switch(s.tag){case 0:case 11:case 15:os(9,s)}}catch(w){Eu(s,s.return,w)}if(s===l){Zl=null;break e}var x=s.sibling;if(null!==x){x.return=s.return,Zl=x;break e}Zl=s.return}}if(Ps=o,Ho(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(ot,e)}catch(w){}r=!0}return r}finally{vt=n,Ns.transition=t}}return!1}function ju(e,t,n){e=Ma(e,t=hl(0,t=cl(n,t),1),1),t=tu(),null!==e&&(bt(e,1,t),ou(e,t))}function Eu(e,t,n){if(3===e.tag)ju(e,e,n);else for(;null!==t;){if(3===t.tag){ju(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===qs||!qs.has(r))){t=Ma(t,e=ml(t,e=cl(n,e),1),1),e=tu(),null!==t&&(bt(t,1,e),ou(t,e));break}}t=t.return}}function Cu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=tu(),e.pingedLanes|=e.suspendedLanes&n,Ts===e&&(zs&n)===n&&(4===Ms||3===Ms&&(130023424&zs)===zs&&500>Ke()-Bs?pu(e,0):$s|=n),ou(e,t)}function _u(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=tu();null!==(e=Ta(e,t))&&(bt(e,t,n),ou(e,n))}function Nu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),_u(e,n)}function Pu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),_u(e,n)}function Tu(e,t){return qe(e,t)}function Lu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function zu(e,t,n,r){return new Lu(e,t,n,r)}function Du(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ru(e,t){var n=e.alternate;return null===n?((n=zu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Mu(e,t,n,r,o,i){var l=2;if(r=e,"function"===typeof e)Du(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case S:return Ou(n.children,o,i,t);case j:l=8,o|=8;break;case E:return(e=zu(12,n,t,2|o)).elementType=E,e.lanes=i,e;case P:return(e=zu(13,n,t,o)).elementType=P,e.lanes=i,e;case T:return(e=zu(19,n,t,o)).elementType=T,e.lanes=i,e;case D:return Iu(n,o,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case C:l=10;break e;case _:l=9;break e;case N:l=11;break e;case L:l=14;break e;case z:l=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=zu(l,n,t,o)).elementType=e,t.type=r,t.lanes=i,t}function Ou(e,t,n,r){return(e=zu(7,e,r,t)).lanes=n,e}function Iu(e,t,n,r){return(e=zu(22,e,r,t)).elementType=D,e.lanes=n,e.stateNode={isHidden:!1},e}function Fu(e,t,n){return(e=zu(6,e,null,t)).lanes=n,e}function $u(e,t,n){return(t=zu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uu(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Au(e,t,n,r,o,a,i,l,s){return e=new Uu(e,t,n,l,s),1===t?(t=1,!0===a&&(t|=8)):t=0,a=zu(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},za(a),e}function Bu(e){if(!e)return No;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Do(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(Do(n))return Oo(e,n,t)}return t}function Hu(e,t,n,r,o,a,i,l,s){return(e=Au(n,r,!0,e,0,a,0,l,s)).context=Bu(null),n=e.current,(a=Ra(r=tu(),o=nu(n))).callback=void 0!==t&&null!==t?t:null,Ma(n,a,o),e.current.lanes=o,bt(e,o,r),ou(e,r),e}function Wu(e,t,n,r){var o=t.current,a=tu(),i=nu(o);return n=Bu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ra(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ma(o,t,i))&&(ru(e,o,i,a),Oa(e,o,i)),i}function Vu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Gu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qu(e,t){Gu(e,t),(e=e.alternate)&&Gu(e,t)}js=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||To.current)kl=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return kl=!1,function(e,t,n){switch(t.tag){case 3:Tl(t),ha();break;case 5:ii(t);break;case 1:Do(t.type)&&Io(t);break;case 4:oi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;_o(ya,r._currentValue),r._currentValue=o;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(_o(si,1&si.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Fl(e,t,n):(_o(si,1&si.current),null!==(e=Vl(e,t,n))?e.sibling:null);_o(si,1&si.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Hl(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),_o(si,si.current),r)break;return null;case 22:case 23:return t.lanes=0,El(e,t,n)}return Vl(e,t,n)}(e,t,n);kl=0!==(131072&e.flags)}else kl=!1,aa&&0!==(1048576&t.flags)&&ea(t,qo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wl(e,t),e=t.pendingProps;var o=zo(t,Po.current);Ea(t,n),o=ji(null,t,r,e,o,n);var i=Ei();return t.flags|=1,"object"===typeof o&&null!==o&&"function"===typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Do(r)?(i=!0,Io(t)):i=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,za(t),o.updater=Ba,t.stateNode=o,o._reactInternals=t,Ga(t,r,e,n),t=Pl(null,t,r,!0,i,n)):(t.tag=0,aa&&i&&ta(t),xl(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wl(e,t),e=t.pendingProps,r=(o=r._init)(r._payload),t.type=r,o=t.tag=function(e){if("function"===typeof e)return Du(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===N)return 11;if(e===L)return 14}return 2}(r),e=ba(r,e),o){case 0:t=_l(null,t,r,e,n);break e;case 1:t=Nl(null,t,r,e,n);break e;case 11:t=wl(null,t,r,e,n);break e;case 14:t=Sl(null,t,r,ba(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,_l(e,t,r,o=t.elementType===r?o:ba(r,o),n);case 1:return r=t.type,o=t.pendingProps,Nl(e,t,r,o=t.elementType===r?o:ba(r,o),n);case 3:e:{if(Tl(t),null===e)throw Error(a(387));r=t.pendingProps,o=(i=t.memoizedState).element,Da(e,t),Fa(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Ll(e,t,r,n,o=cl(Error(a(423)),t));break e}if(r!==o){t=Ll(e,t,r,n,o=cl(Error(a(424)),t));break e}for(oa=uo(t.stateNode.containerInfo.firstChild),ra=t,aa=!0,ia=null,n=Ja(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ha(),r===o){t=Vl(e,t,n);break e}xl(e,t,r,n)}t=t.child}return t;case 5:return ii(t),null===e&&ca(t),r=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,l=o.children,no(r,o)?l=null:null!==i&&no(r,i)&&(t.flags|=32),Cl(e,t),xl(e,t,l,n),t.child;case 6:return null===e&&ca(t),null;case 13:return Fl(e,t,n);case 4:return oi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Ka(t,null,r,n):xl(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,wl(e,t,r,o=t.elementType===r?o:ba(r,o),n);case 7:return xl(e,t,t.pendingProps,n),t.child;case 8:case 12:return xl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,_o(ya,r._currentValue),r._currentValue=l,null!==i)if(lr(i.value,l)){if(i.children===o.children&&!To.current){t=Vl(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var s=i.dependencies;if(null!==s){l=i.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===i.tag){(u=Ra(-1,n&-n)).tag=2;var c=i.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),ja(i.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(a(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),ja(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}xl(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Ea(t,n),r=r(o=Ca(o)),t.flags|=1,xl(e,t,r,n),t.child;case 14:return o=ba(r=t.type,t.pendingProps),Sl(e,t,r,o=ba(r.type,o),n);case 15:return jl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ba(r,o),Wl(e,t),t.tag=1,Do(r)?(e=!0,Io(t)):e=!1,Ea(t,n),Wa(t,r,o),Ga(t,r,o,n),Pl(null,t,r,!0,e,n);case 19:return Hl(e,t,n);case 22:return El(e,t,n)}throw Error(a(156,t.tag))};var Qu="function"===typeof reportError?reportError:function(e){console.error(e)};function Yu(e){this._internalRoot=e}function Xu(e){this._internalRoot=e}function Ku(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Ju(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zu(){}function ec(e,t,n,r,o){var a=n._reactRootContainer;if(a){var i=a;if("function"===typeof o){var l=o;o=function(){var e=Vu(i);l.call(e)}}Wu(t,i,e,o)}else i=function(e,t,n,r,o){if(o){if("function"===typeof r){var a=r;r=function(){var e=Vu(i);a.call(e)}}var i=Hu(t,r,e,0,null,!1,0,"",Zu);return e._reactRootContainer=i,e[mo]=i.current,Br(8===e.nodeType?e.parentNode:e),du(),i}for(;o=e.lastChild;)e.removeChild(o);if("function"===typeof r){var l=r;r=function(){var e=Vu(s);l.call(e)}}var s=Au(e,0,!1,null,0,!1,0,"",Zu);return e._reactRootContainer=s,e[mo]=s.current,Br(8===e.nodeType?e.parentNode:e),du((function(){Wu(t,s,n,r)})),s}(n,t,e,o,r);return Vu(i)}Xu.prototype.render=Yu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));Wu(e,t,null,null)},Xu.prototype.unmount=Yu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;du((function(){Wu(null,e,null,null)})),t[mo]=null}},Xu.prototype.unstable_scheduleHydration=function(e){if(e){var t=jt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Dt.length&&0!==t&&t<Dt[n].priority;n++);Dt.splice(n,0,e),0===n&&It(e)}},xt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),ou(t,Ke()),0===(6&Ps)&&(Hs=Ke()+500,Ho()))}break;case 13:du((function(){var t=Ta(e,1);if(null!==t){var n=tu();ru(t,e,1,n)}})),qu(e,1)}},wt=function(e){if(13===e.tag){var t=Ta(e,134217728);if(null!==t)ru(t,e,134217728,tu());qu(e,134217728)}},St=function(e){if(13===e.tag){var t=nu(e),n=Ta(e,t);if(null!==n)ru(n,e,t,tu());qu(e,t)}},jt=function(){return vt},Et=function(e,t){var n=vt;try{return vt=e,t()}finally{vt=n}},we=function(e,t,n){switch(t){case"input":if(J(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=wo(r);if(!o)throw Error(a(90));q(r),J(r,o)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ne=cu,Pe=du;var tc={usingClientEntryPoint:!1,Events:[ko,xo,wo,Ce,_e,cu]},nc={findFiberByHostInstance:vo,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},rc={bundleType:nc.bundleType,version:nc.version,rendererPackageName:nc.rendererPackageName,rendererConfig:nc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:k.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ve(e))?null:e.stateNode},findFiberByHostInstance:nc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var oc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!oc.isDisabled&&oc.supportsFiber)try{ot=oc.inject(rc),at=oc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Ku(t))throw Error(a(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:w,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Ku(e))throw Error(a(299));var n=!1,r="",o=Qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=Au(e,1,!1,null,0,n,0,r,o),e[mo]=t.current,Br(8===e.nodeType?e.parentNode:e),new Yu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=null===(e=Ve(t))?null:e.stateNode},t.flushSync=function(e){return du(e)},t.hydrate=function(e,t,n){if(!Ju(t))throw Error(a(200));return ec(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Ku(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,o=!1,i="",l=Qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(o=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Hu(t,null,e,1,null!=n?n:null,o,0,i,l),e[mo]=t.current,Br(e),r)for(e=0;e<r.length;e++)o=(o=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Xu(t)},t.render=function(e,t,n){if(!Ju(t))throw Error(a(200));return ec(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Ju(e))throw Error(a(40));return!!e._reactRootContainer&&(du((function(){ec(null,null,e,!1,(function(){e._reactRootContainer=null,e[mo]=null}))})),!0)},t.unstable_batchedUpdates=cu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ju(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return ec(e,t,n,!1,r)},t.version="18.2.0-next-9e3b772b8-20220608"},391:(e,t,n)=>{"use strict";var r=n(950);t.createRoot=r.createRoot,r.hydrateRoot},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},136:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraggableCore",{enumerable:!0,get:function(){return c.default}}),t.default=void 0;var r=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==typeof e&&"function"!==typeof e)return{default:e};var n=p(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=e[a]}r.default=e,n&&n.set(e,r);return r}(n(43)),o=f(n(173)),a=f(n(950)),i=f(n(989)),l=n(212),s=n(777),u=n(447),c=f(n(935)),d=f(n(387));function f(e){return e&&e.__esModule?e:{default:e}}function p(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(p=function(e){return e?n:t})(e)}function h(){return h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},h.apply(this,arguments)}function m(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends r.Component{static getDerivedStateFromProps(e,t){let{position:n}=e,{prevPropsPosition:r}=t;return!n||r&&n.x===r.x&&n.y===r.y?null:((0,d.default)("Draggable: getDerivedStateFromProps %j",{position:n,prevPropsPosition:r}),{x:n.x,y:n.y,prevPropsPosition:{...n}})}constructor(e){super(e),m(this,"onDragStart",((e,t)=>{(0,d.default)("Draggable: onDragStart: %j",t);if(!1===this.props.onStart(e,(0,s.createDraggableData)(this,t)))return!1;this.setState({dragging:!0,dragged:!0})})),m(this,"onDrag",((e,t)=>{if(!this.state.dragging)return!1;(0,d.default)("Draggable: onDrag: %j",t);const n=(0,s.createDraggableData)(this,t),r={x:n.x,y:n.y,slackX:0,slackY:0};if(this.props.bounds){const{x:e,y:t}=r;r.x+=this.state.slackX,r.y+=this.state.slackY;const[o,a]=(0,s.getBoundPosition)(this,r.x,r.y);r.x=o,r.y=a,r.slackX=this.state.slackX+(e-r.x),r.slackY=this.state.slackY+(t-r.y),n.x=r.x,n.y=r.y,n.deltaX=r.x-this.state.x,n.deltaY=r.y-this.state.y}if(!1===this.props.onDrag(e,n))return!1;this.setState(r)})),m(this,"onDragStop",((e,t)=>{if(!this.state.dragging)return!1;if(!1===this.props.onStop(e,(0,s.createDraggableData)(this,t)))return!1;(0,d.default)("Draggable: onDragStop: %j",t);const n={dragging:!1,slackX:0,slackY:0};if(Boolean(this.props.position)){const{x:e,y:t}=this.props.position;n.x=e,n.y=t}this.setState(n)})),this.state={dragging:!1,dragged:!1,x:e.position?e.position.x:e.defaultPosition.x,y:e.position?e.position.y:e.defaultPosition.y,prevPropsPosition:{...e.position},slackX:0,slackY:0,isElementSVG:!1},!e.position||e.onDrag||e.onStop||console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element.")}componentDidMount(){"undefined"!==typeof window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}componentWillUnmount(){this.setState({dragging:!1})}findDOMNode(){var e,t;return null!==(e=null===(t=this.props)||void 0===t||null===(t=t.nodeRef)||void 0===t?void 0:t.current)&&void 0!==e?e:a.default.findDOMNode(this)}render(){const{axis:e,bounds:t,children:n,defaultPosition:o,defaultClassName:a,defaultClassNameDragging:u,defaultClassNameDragged:d,position:f,positionOffset:p,scale:m,...g}=this.props;let b={},y=null;const v=!Boolean(f)||this.state.dragging,k=f||o,x={x:(0,s.canDragX)(this)&&v?this.state.x:k.x,y:(0,s.canDragY)(this)&&v?this.state.y:k.y};this.state.isElementSVG?y=(0,l.createSVGTransform)(x,p):b=(0,l.createCSSTransform)(x,p);const w=(0,i.default)(n.props.className||"",a,{[u]:this.state.dragging,[d]:this.state.dragged});return r.createElement(c.default,h({},g,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),r.cloneElement(r.Children.only(n),{className:w,style:{...n.props.style,...b},transform:y}))}}t.default=g,m(g,"displayName","Draggable"),m(g,"propTypes",{...c.default.propTypes,axis:o.default.oneOf(["both","x","y","none"]),bounds:o.default.oneOfType([o.default.shape({left:o.default.number,right:o.default.number,top:o.default.number,bottom:o.default.number}),o.default.string,o.default.oneOf([!1])]),defaultClassName:o.default.string,defaultClassNameDragging:o.default.string,defaultClassNameDragged:o.default.string,defaultPosition:o.default.shape({x:o.default.number,y:o.default.number}),positionOffset:o.default.shape({x:o.default.oneOfType([o.default.number,o.default.string]),y:o.default.oneOfType([o.default.number,o.default.string])}),position:o.default.shape({x:o.default.number,y:o.default.number}),className:u.dontSetMe,style:u.dontSetMe,transform:u.dontSetMe}),m(g,"defaultProps",{...c.default.defaultProps,axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1})},935:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==typeof e&&"function"!==typeof e)return{default:e};var n=d(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=e[a]}r.default=e,n&&n.set(e,r);return r}(n(43)),o=c(n(173)),a=c(n(950)),i=n(212),l=n(777),s=n(447),u=c(n(387));function c(e){return e&&e.__esModule?e:{default:e}}function d(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(d=function(e){return e?n:t})(e)}function f(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const p={start:"touchstart",move:"touchmove",stop:"touchend"},h={start:"mousedown",move:"mousemove",stop:"mouseup"};let m=h;class g extends r.Component{constructor(){super(...arguments),f(this,"dragging",!1),f(this,"lastX",NaN),f(this,"lastY",NaN),f(this,"touchIdentifier",null),f(this,"mounted",!1),f(this,"handleDragStart",(e=>{if(this.props.onMouseDown(e),!this.props.allowAnyClick&&"number"===typeof e.button&&0!==e.button)return!1;const t=this.findDOMNode();if(!t||!t.ownerDocument||!t.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");const{ownerDocument:n}=t;if(this.props.disabled||!(e.target instanceof n.defaultView.Node)||this.props.handle&&!(0,i.matchesSelectorAndParentsTo)(e.target,this.props.handle,t)||this.props.cancel&&(0,i.matchesSelectorAndParentsTo)(e.target,this.props.cancel,t))return;"touchstart"===e.type&&e.preventDefault();const r=(0,i.getTouchIdentifier)(e);this.touchIdentifier=r;const o=(0,l.getControlPosition)(e,r,this);if(null==o)return;const{x:a,y:s}=o,c=(0,l.createCoreData)(this,a,s);(0,u.default)("DraggableCore: handleDragStart: %j",c),(0,u.default)("calling",this.props.onStart);!1!==this.props.onStart(e,c)&&!1!==this.mounted&&(this.props.enableUserSelectHack&&(0,i.addUserSelectStyles)(n),this.dragging=!0,this.lastX=a,this.lastY=s,(0,i.addEvent)(n,m.move,this.handleDrag),(0,i.addEvent)(n,m.stop,this.handleDragStop))})),f(this,"handleDrag",(e=>{const t=(0,l.getControlPosition)(e,this.touchIdentifier,this);if(null==t)return;let{x:n,y:r}=t;if(Array.isArray(this.props.grid)){let e=n-this.lastX,t=r-this.lastY;if([e,t]=(0,l.snapToGrid)(this.props.grid,e,t),!e&&!t)return;n=this.lastX+e,r=this.lastY+t}const o=(0,l.createCoreData)(this,n,r);(0,u.default)("DraggableCore: handleDrag: %j",o);if(!1!==this.props.onDrag(e,o)&&!1!==this.mounted)this.lastX=n,this.lastY=r;else try{this.handleDragStop(new MouseEvent("mouseup"))}catch(a){const e=document.createEvent("MouseEvents");e.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),this.handleDragStop(e)}})),f(this,"handleDragStop",(e=>{if(!this.dragging)return;const t=(0,l.getControlPosition)(e,this.touchIdentifier,this);if(null==t)return;let{x:n,y:r}=t;if(Array.isArray(this.props.grid)){let e=n-this.lastX||0,t=r-this.lastY||0;[e,t]=(0,l.snapToGrid)(this.props.grid,e,t),n=this.lastX+e,r=this.lastY+t}const o=(0,l.createCoreData)(this,n,r);if(!1===this.props.onStop(e,o)||!1===this.mounted)return!1;const a=this.findDOMNode();a&&this.props.enableUserSelectHack&&(0,i.removeUserSelectStyles)(a.ownerDocument),(0,u.default)("DraggableCore: handleDragStop: %j",o),this.dragging=!1,this.lastX=NaN,this.lastY=NaN,a&&((0,u.default)("DraggableCore: Removing handlers"),(0,i.removeEvent)(a.ownerDocument,m.move,this.handleDrag),(0,i.removeEvent)(a.ownerDocument,m.stop,this.handleDragStop))})),f(this,"onMouseDown",(e=>(m=h,this.handleDragStart(e)))),f(this,"onMouseUp",(e=>(m=h,this.handleDragStop(e)))),f(this,"onTouchStart",(e=>(m=p,this.handleDragStart(e)))),f(this,"onTouchEnd",(e=>(m=p,this.handleDragStop(e))))}componentDidMount(){this.mounted=!0;const e=this.findDOMNode();e&&(0,i.addEvent)(e,p.start,this.onTouchStart,{passive:!1})}componentWillUnmount(){this.mounted=!1;const e=this.findDOMNode();if(e){const{ownerDocument:t}=e;(0,i.removeEvent)(t,h.move,this.handleDrag),(0,i.removeEvent)(t,p.move,this.handleDrag),(0,i.removeEvent)(t,h.stop,this.handleDragStop),(0,i.removeEvent)(t,p.stop,this.handleDragStop),(0,i.removeEvent)(e,p.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,i.removeUserSelectStyles)(t)}}findDOMNode(){var e,t;return null!==(e=this.props)&&void 0!==e&&e.nodeRef?null===(t=this.props)||void 0===t||null===(t=t.nodeRef)||void 0===t?void 0:t.current:a.default.findDOMNode(this)}render(){return r.cloneElement(r.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}t.default=g,f(g,"displayName","DraggableCore"),f(g,"propTypes",{allowAnyClick:o.default.bool,children:o.default.node.isRequired,disabled:o.default.bool,enableUserSelectHack:o.default.bool,offsetParent:function(e,t){if(e[t]&&1!==e[t].nodeType)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:o.default.arrayOf(o.default.number),handle:o.default.string,cancel:o.default.string,nodeRef:o.default.object,onStart:o.default.func,onDrag:o.default.func,onStop:o.default.func,onMouseDown:o.default.func,scale:o.default.number,className:s.dontSetMe,style:s.dontSetMe,transform:s.dontSetMe}),f(g,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1})},929:(e,t,n)=>{"use strict";const{default:r,DraggableCore:o}=n(136);e.exports=r,e.exports.default=r,e.exports.DraggableCore=o},212:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addClassName=u,t.addEvent=function(e,t,n,r){if(!e)return;const o={capture:!0,...r};e.addEventListener?e.addEventListener(t,n,o):e.attachEvent?e.attachEvent("on"+t,n):e["on"+t]=n},t.addUserSelectStyles=function(e){if(!e)return;let t=e.getElementById("react-draggable-style-el");t||(t=e.createElement("style"),t.type="text/css",t.id="react-draggable-style-el",t.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",t.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",e.getElementsByTagName("head")[0].appendChild(t));e.body&&u(e.body,"react-draggable-transparent-selection")},t.createCSSTransform=function(e,t){const n=s(e,t,"px");return{[(0,o.browserPrefixToKey)("transform",o.default)]:n}},t.createSVGTransform=function(e,t){return s(e,t,"")},t.getTouch=function(e,t){return e.targetTouches&&(0,r.findInArray)(e.targetTouches,(e=>t===e.identifier))||e.changedTouches&&(0,r.findInArray)(e.changedTouches,(e=>t===e.identifier))},t.getTouchIdentifier=function(e){if(e.targetTouches&&e.targetTouches[0])return e.targetTouches[0].identifier;if(e.changedTouches&&e.changedTouches[0])return e.changedTouches[0].identifier},t.getTranslation=s,t.innerHeight=function(e){let t=e.clientHeight;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,r.int)(n.paddingTop),t-=(0,r.int)(n.paddingBottom),t},t.innerWidth=function(e){let t=e.clientWidth;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,r.int)(n.paddingLeft),t-=(0,r.int)(n.paddingRight),t},t.matchesSelector=l,t.matchesSelectorAndParentsTo=function(e,t,n){let r=e;do{if(l(r,t))return!0;if(r===n)return!1;r=r.parentNode}while(r);return!1},t.offsetXYFromParent=function(e,t,n){const r=t===t.ownerDocument.body?{left:0,top:0}:t.getBoundingClientRect(),o=(e.clientX+t.scrollLeft-r.left)/n,a=(e.clientY+t.scrollTop-r.top)/n;return{x:o,y:a}},t.outerHeight=function(e){let t=e.clientHeight;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,r.int)(n.borderTopWidth),t+=(0,r.int)(n.borderBottomWidth),t},t.outerWidth=function(e){let t=e.clientWidth;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,r.int)(n.borderLeftWidth),t+=(0,r.int)(n.borderRightWidth),t},t.removeClassName=c,t.removeEvent=function(e,t,n,r){if(!e)return;const o={capture:!0,...r};e.removeEventListener?e.removeEventListener(t,n,o):e.detachEvent?e.detachEvent("on"+t,n):e["on"+t]=null},t.removeUserSelectStyles=function(e){if(!e)return;try{if(e.body&&c(e.body,"react-draggable-transparent-selection"),e.selection)e.selection.empty();else{const t=(e.defaultView||window).getSelection();t&&"Caret"!==t.type&&t.removeAllRanges()}}catch(t){}};var r=n(447),o=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==typeof e&&"function"!==typeof e)return{default:e};var n=a(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=o?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(r,i,l):r[i]=e[i]}r.default=e,n&&n.set(e,r);return r}(n(561));function a(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(a=function(e){return e?n:t})(e)}let i="";function l(e,t){return i||(i=(0,r.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],(function(t){return(0,r.isFunction)(e[t])}))),!!(0,r.isFunction)(e[i])&&e[i](t)}function s(e,t,n){let{x:r,y:o}=e,a="translate(".concat(r).concat(n,",").concat(o).concat(n,")");if(t){const e="".concat("string"===typeof t.x?t.x:t.x+n),r="".concat("string"===typeof t.y?t.y:t.y+n);a="translate(".concat(e,", ").concat(r,")")+a}return a}function u(e,t){e.classList?e.classList.add(t):e.className.match(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)")))||(e.className+=" ".concat(t))}function c(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)"),"g"),"")}},561:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.browserPrefixToKey=o,t.browserPrefixToStyle=function(e,t){return t?"-".concat(t.toLowerCase(),"-").concat(e):e},t.default=void 0,t.getPrefix=r;const n=["Moz","Webkit","O","ms"];function r(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"===typeof window)return"";const r=null===(e=window.document)||void 0===e||null===(e=e.documentElement)||void 0===e?void 0:e.style;if(!r)return"";if(t in r)return"";for(let a=0;a<n.length;a++)if(o(t,n[a])in r)return n[a];return""}function o(e,t){return t?"".concat(t).concat(function(e){let t="",n=!0;for(let r=0;r<e.length;r++)n?(t+=e[r].toUpperCase(),n=!1):"-"===e[r]?n=!0:t+=e[r];return t}(e)):e}t.default=r()},387:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){0}},777:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.canDragX=function(e){return"both"===e.props.axis||"x"===e.props.axis},t.canDragY=function(e){return"both"===e.props.axis||"y"===e.props.axis},t.createCoreData=function(e,t,n){const o=!(0,r.isNum)(e.lastX),i=a(e);return o?{node:i,deltaX:0,deltaY:0,lastX:t,lastY:n,x:t,y:n}:{node:i,deltaX:t-e.lastX,deltaY:n-e.lastY,lastX:e.lastX,lastY:e.lastY,x:t,y:n}},t.createDraggableData=function(e,t){const n=e.props.scale;return{node:t.node,x:e.state.x+t.deltaX/n,y:e.state.y+t.deltaY/n,deltaX:t.deltaX/n,deltaY:t.deltaY/n,lastX:e.state.x,lastY:e.state.y}},t.getBoundPosition=function(e,t,n){if(!e.props.bounds)return[t,n];let{bounds:i}=e.props;i="string"===typeof i?i:function(e){return{left:e.left,top:e.top,right:e.right,bottom:e.bottom}}(i);const l=a(e);if("string"===typeof i){const{ownerDocument:e}=l,t=e.defaultView;let n;if(n="parent"===i?l.parentNode:e.querySelector(i),!(n instanceof t.HTMLElement))throw new Error('Bounds selector "'+i+'" could not find an element.');const a=n,s=t.getComputedStyle(l),u=t.getComputedStyle(a);i={left:-l.offsetLeft+(0,r.int)(u.paddingLeft)+(0,r.int)(s.marginLeft),top:-l.offsetTop+(0,r.int)(u.paddingTop)+(0,r.int)(s.marginTop),right:(0,o.innerWidth)(a)-(0,o.outerWidth)(l)-l.offsetLeft+(0,r.int)(u.paddingRight)-(0,r.int)(s.marginRight),bottom:(0,o.innerHeight)(a)-(0,o.outerHeight)(l)-l.offsetTop+(0,r.int)(u.paddingBottom)-(0,r.int)(s.marginBottom)}}(0,r.isNum)(i.right)&&(t=Math.min(t,i.right));(0,r.isNum)(i.bottom)&&(n=Math.min(n,i.bottom));(0,r.isNum)(i.left)&&(t=Math.max(t,i.left));(0,r.isNum)(i.top)&&(n=Math.max(n,i.top));return[t,n]},t.getControlPosition=function(e,t,n){const r="number"===typeof t?(0,o.getTouch)(e,t):null;if("number"===typeof t&&!r)return null;const i=a(n),l=n.props.offsetParent||i.offsetParent||i.ownerDocument.body;return(0,o.offsetXYFromParent)(r||e,l,n.props.scale)},t.snapToGrid=function(e,t,n){const r=Math.round(t/e[0])*e[0],o=Math.round(n/e[1])*e[1];return[r,o]};var r=n(447),o=n(212);function a(e){const t=e.findDOMNode();if(!t)throw new Error("<DraggableCore>: Unmounted during event!");return t}},447:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.dontSetMe=function(e,t,n){if(e[t])return new Error("Invalid prop ".concat(t," passed to ").concat(n," - do not set this, set it on the child."))},t.findInArray=function(e,t){for(let n=0,r=e.length;n<r;n++)if(t.apply(t,[e[n],n,e]))return e[n]},t.int=function(e){return parseInt(e,10)},t.isFunction=function(e){return"function"===typeof e||"[object Function]"===Object.prototype.toString.call(e)},t.isNum=function(e){return"number"===typeof e&&!isNaN(e)}},989:(e,t,n)=>{"use strict";function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n);else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function o(){for(var e,t,n=0,o="";n<arguments.length;)(e=arguments[n++])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o}n.r(t),n.d(t,{clsx:()=>o,default:()=>a});const a=o},153:(e,t,n)=>{"use strict";var r=n(43),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,a={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:u,ref:c,props:a,_owner:l.current}}t.Fragment=a,t.jsx=u,t.jsxs=u},202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function y(){}function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=b.prototype;var k=v.prototype=new y;k.constructor=v,m(k,b.prototype),k.isPureReactComponent=!0;var x=Array.isArray,w=Object.prototype.hasOwnProperty,S={current:null},j={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var o,a={},i=null,l=null;if(null!=t)for(o in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)w.call(t,o)&&!j.hasOwnProperty(o)&&(a[o]=t[o]);var s=arguments.length-2;if(1===s)a.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];a.children=u}if(e&&e.defaultProps)for(o in s=e.defaultProps)void 0===a[o]&&(a[o]=s[o]);return{$$typeof:n,type:e,key:i,ref:l,props:a,_owner:S.current}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var _=/\/+/g;function N(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(e,t,o,a,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return i=i(s=e),e=""===a?"."+N(s,0):a,x(i)?(o="",null!=e&&(o=e.replace(_,"$&/")+"/"),P(i,t,o,"",(function(e){return e}))):null!=i&&(C(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(_,"$&/")+"/")+e)),t.push(i)),1;if(s=0,a=""===a?".":a+":",x(e))for(var u=0;u<e.length;u++){var c=a+N(l=e[u],u);s+=P(l,t,o,c,i)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(l=e.next()).done;)s+=P(l=l.value,t,o,c=a+N(l,u++),i);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function T(e,t,n){if(null==e)return e;var r=[],o=0;return P(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function L(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var z={current:null},D={transition:null},R={ReactCurrentDispatcher:z,ReactCurrentBatchConfig:D,ReactCurrentOwner:S};t.Children={map:T,forEach:function(e,t,n){T(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return T(e,(function(){t++})),t},toArray:function(e){return T(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=o,t.Profiler=i,t.PureComponent=v,t.StrictMode=a,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=R,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),a=e.key,i=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,l=S.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)w.call(t,u)&&!j.hasOwnProperty(u)&&(o[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)o.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];o.children=s}return{$$typeof:n,type:e.type,key:a,ref:i,props:o,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=D.transition;D.transition={};try{e()}finally{D.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.useCallback=function(e,t){return z.current.useCallback(e,t)},t.useContext=function(e){return z.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return z.current.useDeferredValue(e)},t.useEffect=function(e,t){return z.current.useEffect(e,t)},t.useId=function(){return z.current.useId()},t.useImperativeHandle=function(e,t,n){return z.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return z.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return z.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return z.current.useMemo(e,t)},t.useReducer=function(e,t,n){return z.current.useReducer(e,t,n)},t.useRef=function(e){return z.current.useRef(e)},t.useState=function(e){return z.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return z.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return z.current.useTransition()},t.version="18.2.0"},43:(e,t,n)=>{"use strict";e.exports=n(202)},579:(e,t,n)=>{"use strict";e.exports=n(153)},234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var l=2*(r+1)-1,s=e[l],u=l+1,c=e[u];if(0>a(s,n))u<o&&0>a(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[l]=n,r=l);else{if(!(u<o&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,b="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,v="undefined"!==typeof setImmediate?setImmediate:null;function k(e){for(var t=r(c);null!==t;){if(null===t.callback)o(c);else{if(!(t.startTime<=e))break;o(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function x(e){if(g=!1,k(e),!m)if(null!==r(u))m=!0,D(w);else{var t=r(c);null!==t&&R(x,t.startTime-e)}}function w(e,n){m=!1,g&&(g=!1,y(C),C=-1),h=!0;var a=p;try{for(k(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!P());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var l=i(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof l?f.callback=l:f===r(u)&&o(u),k(n)}else o(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&R(x,d.startTime-n),s=!1}return s}finally{f=null,p=a,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,j=!1,E=null,C=-1,_=5,N=-1;function P(){return!(t.unstable_now()-N<_)}function T(){if(null!==E){var e=t.unstable_now();N=e;var n=!0;try{n=E(!0,e)}finally{n?S():(j=!1,E=null)}}else j=!1}if("function"===typeof v)S=function(){v(T)};else if("undefined"!==typeof MessageChannel){var L=new MessageChannel,z=L.port2;L.port1.onmessage=T,S=function(){z.postMessage(null)}}else S=function(){b(T,0)};function D(e){E=e,j||(j=!0,S())}function R(e,n){C=b((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,D(w))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?i+a:i:a=i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:l=a+l,sortIndex:-1},a>i?(e.sortIndex=a,n(c,e),null===r(u)&&e===r(c)&&(g?(y(C),C=-1):g=!0,R(x,a-i))):(e.sortIndex=l,n(u,e),m||h||(m=!0,D(w))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},853:(e,t,n)=>{"use strict";e.exports=n(234)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e=n(929),t=n.n(e),r=n(43);const o=["javascript","typescript","react","angular","vue","node","express","python","django","flask","java","spring","rust","go","golang","ruby","rails","php","laravel","c#",".net","aws","azure","gcp","docker","kubernetes","sql","nosql","mongodb","postgresql","mysql","graphql","rest","html","css","sass","less","tailwind","bootstrap"],a=()=>{const e=(()=>{const e=["h1.job-title","h1.posting-title",'h1[data-testid="job-title"]',"h1.topcard__title",".job-details-jobs-unified-top-card__job-title",".jobs-unified-top-card__job-title",".top-card-layout__title",".jobsearch-JobInfoHeader-title",'h1[class*="heading_Level1"]',"h1.text-3xl.font-semibold.text-primary","h1.section-header.section-header--large.font-primary","h1.mb-3.w-max.max-w-5xl.text-3xl.font-medium.text-gray-900","h1.ashby-job-posting-heading","div.job__title h1","h2.jv-header","div.posting-headline h2",'h2[data-automation-id="jobPostingHeader"]',"#header h1.app-title","h1.jobsearch-JobInfoHeader-title span","div.job-header div.container h1","div#jobdetails h1"];for(const o of e){const e=document.querySelector(o);if(e&&e.textContent)return e.textContent.trim()}const t=document.querySelectorAll("h1");for(const o of t){var n;const e=(null===(n=o.textContent)||void 0===n?void 0:n.toLowerCase())||"";var r;if(e.includes("software")||e.includes("developer")||e.includes("engineer")||e.includes("position"))return(null===(r=o.textContent)||void 0===r?void 0:r.trim())||"Unknown Job Title"}return"Unknown Job Title"})(),t=(()=>{const e=[".company-name",".topcard__org-name-link",".job-details-jobs-unified-top-card__company-name",".jobs-unified-top-card__company-name",".topcard__org-name-link",".jobsearch-InlineCompanyRating > div:first-child",".employer-name",'a[data-tracking-control-name="public_jobs_topcard-org-name"]','a[data-tracking-control-name="public_jobs_topcard_org_name"]','h4[class*="heading_Subhead__"]',"h2.text-lg.font-semibold.text-center.text-primary.mb-1.mt-2 a","a.hiring_company","a.font-medium.text-gray-700",'img[class*="_navLogoWordmarkImage"]',"h1.jv-logo a","div#header span.company-name",'div[data-testid="jobsearch-CompanyInfoContainer"] div a',"div.job-board-listing-header div.container h1.brand-text a",'div#jobdetails a[data-cy="companyNameLink"]'];for(const o of e){const e=document.querySelector(o);if(e&&e.textContent)return window.location.href.includes("boards.greenhouse.io")&&!window.location.href.includes("job-boards.greenhouse.io")?e.textContent.trim().split("at")[1].trim():e.textContent.toLowerCase().includes("careers")?e.textContent.trim().split("careers")[0].trim():e.textContent.trim();if(e&&"IMG"===e.tagName)return e.alt.trim()}if(window.location.href.includes("greenhouse.io"))return document.title.split(" at ")[1];return window.location.href.includes("jobs.lever.co")?document.title.split(" - ")[0]:window.location.href.includes("myworkdayjobs.com")?null===(t=JSON.parse((null===(r=document.querySelector('script[type="application/ld+json"]'))||void 0===r?void 0:r.textContent)||"{}"))||void 0===t||null===(n=t.hiringOrganization)||void 0===n?void 0:n.name:"Unknown Company";var t,n,r})(),n=(()=>{const e=[".job-description",".description__text",".jobsearch-jobDescriptionText","#js-greenhouse-job-description","#job-details",".jobs-description-content",".jobs-description__content","#jobDescriptionText",".jobDescriptionContent",'[data-automation="jobDescriptionSection"]','div[class*="description"]','section[class*="description"]','div[class*="JobDetails_jobDescription__"',"div.bg-primary.flex.flex-col.items-start.rounded-lg.p-4.border-gray-800","div.job_content","section.container.mx-auto.px-4.pb-16",'div[class*="_descriptionText_"]',"div.job__description.body","div.jv-job-detail-description",'div.section.page-centered[data-qa="job-description"]','div[data-automation-id="jobPostingDescription"]',"div#content","div#jobDescriptionText","div#job-description","section.job-description","section.company_description"];for(const n of e){var t;const e=document.querySelector(n);if(e&&null!==(t=e.textContent)&&void 0!==t&&t.trim())return e.innerHTML.trim()}return"No job description found"})();return{jobRole:e,companyName:t,jobDescription:n,remoteAvailability:(e=>{const t=e.toLowerCase();return t.includes("remote only")||t.includes("fully remote")||t.includes("100% remote")?"Remote Only":t.includes("hybrid")||t.includes("flexible")?"Hybrid":t.includes("remote")||t.includes("work from home")||t.includes("wfh")?"Remote Possible":t.includes("onsite")||t.includes("on-site")||t.includes("in office")?"On-site":"Not Specified"})(n),techStacks:(e=>{const t=e.toLowerCase();return o.filter((e=>t.includes(e.toLowerCase())))})(n),jobUrl:window.location.href}},i=async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return new Promise(((n,r)=>{chrome.runtime.sendMessage({type:"AI_JOB_PARSE",prompt:e,isQuestion:t},(e=>{chrome.runtime.lastError?r(new Error(chrome.runtime.lastError.message)):e&&!1===e.success?r(new Error(e.error||"AI parsing failed")):n(e)}))}))}catch(n){throw console.error("Error in AI job parsing request:",n),n}},l=e=>new Promise((t=>setTimeout(t,e))),s=["www.glassdoor.com","www.remoterocketship.com","www.ziprecruiter.com","himalayas.app","jobs.ashbyhq.com","job-boards.greenhouse.io","boards.greenhouse.io","jobs.jobvite.com","jobs.lever.co","myworkdayjobs.com","www.indeed.com","applytojob.com","www.dice.com"],u=async()=>{try{const e=window.location.href;if(s.some((t=>e.includes(t))))return a();const t=`\nYou are a JSON-only assistant. Do not include any explanation or extra text.\n\nExtract job information from the following job posting. Output must be a valid JSON object only.\n\nFields to extract:\n- jobTitle\n- companyName\n- companyNationality\n- companySite\n- remoteAvailability (Remote Only, Hybrid, Remote Possible, On-site, Not Specified)\n- techStacks (Array of strings)\n- jobDescription (structured full description with bullets, some symbols to easy to see overview in Markdown and max limit 3000 letters)\n\nReturn in this exact JSON structure:\n\n{\n  "jobTitle": "",\n  "companyName": "",\n  "companyNationality": "",\n  "companySite": "",\n  "remoteAvailability": "",\n  "techStacks": [],\n  "jobDescription": ""\n}\n\n--- BEGIN JOB INFO(Url or content) ---\n${((()=>{let e="";return e=window.location.href.includes("indeed.com")?document.getElementsByClassName("jobsearch-JobComponent")[0].textContent||"":document.body.innerText||"",e})()||e).slice(0,6e3)}\n--- END JOB INFO ---\n    - note when picking infos\n        . jobTitle should be position title.\n        . try to get companyNationality and companySite(link to the company's website) if applicable but optional\n        . jobDescription should include all the important informations like key requirement, company introduction and salary, etc in structured way\n        . companyName should be company who are looking for candidate, not job site's name you can find it from job description\n`;let n;for(let r=0;r<5&&(n=await i(t),null===n);r++)await l(1e3);return console.log("parsed",n),null===n||void 0===n||"{}"===JSON.stringify(n)?(console.error("Failed"),{success:!1,message:"Failed to fetch job details"}):!1===n.success?n:{jobRole:n.jobTitle||"Unknown Job Title",companyName:n.companyName||"Unknown Company",jobDescription:n.jobDescription||"No job description found",remoteAvailability:n.remoteAvailability||"Not Specified",techStacks:n.techStacks||[],jobUrl:e}}catch(e){return console.error("Failed",e),{success:!1,message:"Failed to fetch job details",error:e instanceof Error?e.message:"Unknown error occurred"}}};var c=n(391);class d{source;flags;constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";this.source=e.source,this.flags=t}setGroup(e,t){let n="string"==typeof t?t:t.source;return n=n.replace(/(^|[^\[])\^/g,"$1"),this.source=this.source.replace(e,n),this}getRegexp(){return new RegExp(this.source,this.flags)}}const f=/[&<>"']/,p=/[&<>"']/g,h={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},m=/[<>"']|&(?!#?\w+;)/,g=/[<>"']|&(?!#?\w+;)/g;function b(e,t){if(t){if(f.test(e))return e.replace(p,(e=>h[e]))}else if(m.test(e))return e.replace(g,(e=>h[e]));return e}function y(e){return e.replace(/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,(function(e,t){return"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""}))}var v;!function(e){e[e.space=1]="space",e[e.text=2]="text",e[e.paragraph=3]="paragraph",e[e.heading=4]="heading",e[e.listStart=5]="listStart",e[e.listEnd=6]="listEnd",e[e.looseItemStart=7]="looseItemStart",e[e.looseItemEnd=8]="looseItemEnd",e[e.listItemStart=9]="listItemStart",e[e.listItemEnd=10]="listItemEnd",e[e.blockquoteStart=11]="blockquoteStart",e[e.blockquoteEnd=12]="blockquoteEnd",e[e.code=13]="code",e[e.table=14]="table",e[e.html=15]="html",e[e.hr=16]="hr"}(v||(v={}));class k{gfm=!0;tables=!0;breaks=!1;pedantic=!1;sanitize=!1;sanitizer;mangle=!0;smartLists=!1;silent=!1;highlight;langPrefix="lang-";smartypants=!1;headerPrefix="";renderer;xhtml=!1;escape=b;unescape=y;isNoP}class x{options;constructor(e){this.options=e||j.options}code(e,t,n,r){if(this.options.highlight){const r=this.options.highlight(e,t);null!=r&&r!==e&&(n=!0,e=r)}const o=n?e:this.options.escape(e,!0);if(!t)return`\n<pre><code>${o}\n</code></pre>\n`;return`\n<pre><code class="${this.options.langPrefix+this.options.escape(t,!0)}">${o}\n</code></pre>\n`}blockquote(e){return`<blockquote>\n${e}</blockquote>\n`}html(e){return e}heading(e,t,n){return`<h${t} id="${this.options.headerPrefix+n.toLowerCase().replace(/[^\w]+/g,"-")}">${e}</h${t}>\n`}hr(){return this.options.xhtml?"<hr/>\n":"<hr>\n"}list(e,t){const n=t?"ol":"ul";return`\n<${n}>\n${e}</${n}>\n`}listitem(e){return"<li>"+e+"</li>\n"}paragraph(e){return"<p>"+e+"</p>\n"}table(e,t){return`\n<table>\n<thead>\n${e}</thead>\n<tbody>\n${t}</tbody>\n</table>\n`}tablerow(e){return"<tr>\n"+e+"</tr>\n"}tablecell(e,t){const n=t.header?"th":"td";return(t.align?"<"+n+' style="text-align:'+t.align+'">':"<"+n+">")+e+"</"+n+">\n"}strong(e){return"<strong>"+e+"</strong>"}em(e){return"<em>"+e+"</em>"}codespan(e){return"<code>"+e+"</code>"}br(){return this.options.xhtml?"<br/>":"<br>"}del(e){return"<del>"+e+"</del>"}link(e,t,n){if(this.options.sanitize){let t;try{t=decodeURIComponent(this.options.unescape(e)).replace(/[^\w:]/g,"").toLowerCase()}catch(o){return n}if(0===t.indexOf("javascript:")||0===t.indexOf("vbscript:")||0===t.indexOf("data:"))return n}let r='<a href="'+e+'"';return t&&(r+=' title="'+t+'"'),r+=">"+n+"</a>",r}image(e,t,n){let r='<img src="'+e+'" alt="'+n+'"';return t&&(r+=' title="'+t+'"'),r+=this.options.xhtml?"/>":">",r}text(e){return e}}class w{staticThis;links;options;static rulesBase=null;static rulesPedantic=null;static rulesGfm=null;static rulesBreaks=null;rules;renderer;inLink;hasRulesGfm;ruleCallbacks;constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:j.options,r=arguments.length>3?arguments[3]:void 0;if(this.staticThis=e,this.links=t,this.options=n,this.renderer=r||this.options.renderer||new x(this.options),!this.links)throw new Error("InlineLexer requires 'links' parameter.");this.setRules()}static output(e,t,n){return new this(this,t,n).output(e)}static getRulesBase(){if(this.rulesBase)return this.rulesBase;const e={escape:/^\\([\\`*{}\[\]()#+\-.!_>])/,autolink:/^<([^ <>]+(@|:\/)[^ <>]+)>/,tag:/^<!--[\s\S]*?-->|^<\/?\w+(?:"[^"]*"|'[^']*'|[^<'">])*?>/,link:/^!?\[(inside)\]\(href\)/,reflink:/^!?\[(inside)\]\s*\[([^\]]*)\]/,nolink:/^!?\[((?:\[[^\]]*\]|[^\[\]])*)\]/,strong:/^__([\s\S]+?)__(?!_)|^\*\*([\s\S]+?)\*\*(?!\*)/,em:/^\b_((?:[^_]|__)+?)_\b|^\*((?:\*\*|[\s\S])+?)\*(?!\*)/,code:/^(`+)([\s\S]*?[^`])\1(?!`)/,br:/^ {2,}\n(?!\s*$)/,text:/^[\s\S]+?(?=[\\<!\[_*`]| {2,}\n|$)/,_inside:/(?:\[[^\]]*\]|[^\[\]]|\](?=[^\[]*\]))*/,_href:/\s*<?([\s\S]*?)>?(?:\s+['"]([\s\S]*?)['"])?\s*/};return e.link=new d(e.link).setGroup("inside",e._inside).setGroup("href",e._href).getRegexp(),e.reflink=new d(e.reflink).setGroup("inside",e._inside).getRegexp(),this.rulesBase=e}static getRulesPedantic(){return this.rulesPedantic?this.rulesPedantic:this.rulesPedantic={...this.getRulesBase(),strong:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,em:/^_(?=\S)([\s\S]*?\S)_(?!_)|^\*(?=\S)([\s\S]*?\S)\*(?!\*)/}}static getRulesGfm(){if(this.rulesGfm)return this.rulesGfm;const e=this.getRulesBase(),t=new d(e.escape).setGroup("])","~|])").getRegexp(),n=new d(e.text).setGroup("]|","~]|").setGroup("|","|https?://|").getRegexp();return this.rulesGfm={...e,escape:t,url:/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,del:/^~~(?=\S)([\s\S]*?\S)~~/,text:n}}static getRulesBreaks(){if(this.rulesBreaks)return this.rulesBreaks;const e=this.getRulesGfm(),t=this.getRulesGfm();return this.rulesBreaks={...t,br:new d(e.br).setGroup("{2,}","*").getRegexp(),text:new d(t.text).setGroup("{2,}","*").getRegexp()}}setRules(){this.options.gfm?this.options.breaks?this.rules=this.staticThis.getRulesBreaks():this.rules=this.staticThis.getRulesGfm():this.options.pedantic?this.rules=this.staticThis.getRulesPedantic():this.rules=this.staticThis.getRulesBase(),this.hasRulesGfm=void 0!==this.rules.url}output(e){let t,n="";for(;e;)if(t=this.rules.escape.exec(e))e=e.substring(t[0].length),n+=t[1];else if(t=this.rules.autolink.exec(e)){let r,o;e=e.substring(t[0].length),"@"===t[2]?(r=this.options.escape(":"===t[1].charAt(6)?this.mangle(t[1].substring(7)):this.mangle(t[1])),o=this.mangle("mailto:")+r):(r=this.options.escape(t[1]),o=r),n+=this.renderer.link(o,null,r)}else if(!this.inLink&&this.hasRulesGfm&&(t=this.rules.url.exec(e))){e=e.substring(t[0].length);const r=this.options.escape(t[1]),o=r;n+=this.renderer.link(o,null,r)}else if(t=this.rules.tag.exec(e))!this.inLink&&/^<a /i.test(t[0])?this.inLink=!0:this.inLink&&/^<\/a>/i.test(t[0])&&(this.inLink=!1),e=e.substring(t[0].length),n+=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):this.options.escape(t[0]):t[0];else if(t=this.rules.link.exec(e))e=e.substring(t[0].length),this.inLink=!0,n+=this.outputLink(t,{href:t[2],title:t[3]}),this.inLink=!1;else if((t=this.rules.reflink.exec(e))||(t=this.rules.nolink.exec(e))){e=e.substring(t[0].length);const r=(t[2]||t[1]).replace(/\s+/g," "),o=this.links[r.toLowerCase()];if(!o||!o.href){n+=t[0].charAt(0),e=t[0].substring(1)+e;continue}this.inLink=!0,n+=this.outputLink(t,o),this.inLink=!1}else if(t=this.rules.strong.exec(e))e=e.substring(t[0].length),n+=this.renderer.strong(this.output(t[2]||t[1]));else if(t=this.rules.em.exec(e))e=e.substring(t[0].length),n+=this.renderer.em(this.output(t[2]||t[1]));else if(t=this.rules.code.exec(e))e=e.substring(t[0].length),n+=this.renderer.codespan(this.options.escape(t[2].trim(),!0));else if(t=this.rules.br.exec(e))e=e.substring(t[0].length),n+=this.renderer.br();else if(this.hasRulesGfm&&(t=this.rules.del.exec(e)))e=e.substring(t[0].length),n+=this.renderer.del(this.output(t[1]));else if(t=this.rules.text.exec(e))e=e.substring(t[0].length),n+=this.renderer.text(this.options.escape(this.smartypants(t[0])));else if(e)throw new Error("Infinite loop on byte: "+e.charCodeAt(0));return n}outputLink(e,t){const n=this.options.escape(t.href),r=t.title?this.options.escape(t.title):null;return"!"!==e[0].charAt(0)?this.renderer.link(n,r,this.output(e[1])):this.renderer.image(n,r,this.options.escape(e[1]))}smartypants(e){return this.options.smartypants?e.replace(/---/g,"\u2014").replace(/--/g,"\u2013").replace(/(^|[-\u2014/([{"\s])'/g,"$1\u2018").replace(/'/g,"\u2019").replace(/(^|[-\u2014/([{\u2018\s])"/g,"$1\u201c").replace(/"/g,"\u201d").replace(/\.{3}/g,"\u2026"):e}mangle(e){if(!this.options.mangle)return e;let t="";const n=e.length;for(let r=0;r<n;r++){let n;Math.random()>.5&&(n="x"+e.charCodeAt(r).toString(16)),t+="&#"+n+";"}return t}}class S{simpleRenderers=[];tokens;token;inlineLexer;options;renderer;line=0;constructor(e){this.tokens=[],this.token=null,this.options=e||j.options,this.renderer=this.options.renderer||new x(this.options)}static parse(e,t,n){return new this(n).parse(t,e)}parse(e,t){this.inlineLexer=new w(w,e,this.options,this.renderer),this.tokens=t.reverse();let n="";for(;this.next();)n+=this.tok();return n}debug(e,t){this.inlineLexer=new w(w,e,this.options,this.renderer),this.tokens=t.reverse();let n="";for(;this.next();){const e=this.tok();this.token.line=this.line+=e.split("\n").length-1,n+=e}return n}next(){return this.token=this.tokens.pop()}getNextElement(){return this.tokens[this.tokens.length-1]}parseText(){let e,t=this.token.text;for(;(e=this.getNextElement())&&e.type==v.text;)t+="\n"+this.next().text;return this.inlineLexer.output(t)}tok(){switch(this.token.type){case v.space:return"";case v.paragraph:return this.renderer.paragraph(this.inlineLexer.output(this.token.text));case v.text:return this.options.isNoP?this.parseText():this.renderer.paragraph(this.parseText());case v.heading:return this.renderer.heading(this.inlineLexer.output(this.token.text),this.token.depth,this.token.text);case v.listStart:{let e="";const t=this.token.ordered;for(;this.next().type!=v.listEnd;)e+=this.tok();return this.renderer.list(e,t)}case v.listItemStart:{let e="";for(;this.next().type!=v.listItemEnd;)e+=this.token.type==v.text?this.parseText():this.tok();return this.renderer.listitem(e)}case v.looseItemStart:{let e="";for(;this.next().type!=v.listItemEnd;)e+=this.tok();return this.renderer.listitem(e)}case v.code:return this.renderer.code(this.token.text,this.token.lang,this.token.escaped,this.token.meta);case v.table:{let e,t="",n="";e="";for(let r=0;r<this.token.header.length;r++){const t={header:!0,align:this.token.align[r]},n=this.inlineLexer.output(this.token.header[r]);e+=this.renderer.tablecell(n,t)}t+=this.renderer.tablerow(e);for(const r of this.token.cells){e="";for(let t=0;t<r.length;t++)e+=this.renderer.tablecell(this.inlineLexer.output(r[t]),{header:!1,align:this.token.align[t]});n+=this.renderer.tablerow(e)}return this.renderer.table(t,n)}case v.blockquoteStart:{let e="";for(;this.next().type!=v.blockquoteEnd;)e+=this.tok();return this.renderer.blockquote(e)}case v.hr:return this.renderer.hr();case v.html:{const e=this.token.pre||this.options.pedantic?this.token.text:this.inlineLexer.output(this.token.text);return this.renderer.html(e)}default:{if(this.simpleRenderers.length)for(let t=0;t<this.simpleRenderers.length;t++)if(this.token.type=="simpleRule"+(t+1))return this.simpleRenderers[t].call(this.renderer,this.token.execArr);const e=`Token with "${this.token.type}" type was not found.`;if(!this.options.silent)throw new Error(e);console.log(e)}}}}class j{static options=new k;static simpleRenderers=[];static setOptions(e){return Object.assign(this.options,e),this}static setBlockRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>"";return E.simpleRules.push(e),this.simpleRenderers.push(t),this}static parse(e,t){try{t={...this.options,...t};const{tokens:n,links:r}=this.callBlockLexer(e,t);return this.callParser(n,r,t)}catch(n){return this.callMe(n)}}static debug(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options;const{tokens:n,links:r}=this.callBlockLexer(e,t);let o=n.slice();const a=new S(t);a.simpleRenderers=this.simpleRenderers;const i=a.debug(r,n);return o=o.map((e=>{e.type=v[e.type]||e.type;const t=e.line;return delete e.line,t?{line:t,...e}:e})),{tokens:o,links:r,result:i}}static callBlockLexer(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0;if("string"!=typeof e)throw new Error(`Expected that the 'src' parameter would have a 'string' type, got '${typeof e}'`);return e=e.replace(/\r\n|\r/g,"\n").replace(/\t/g,"    ").replace(/\u00a0/g," ").replace(/\u2424/g,"\n").replace(/^ +$/gm,""),E.lex(e,t,!0)}static callParser(e,t,n){if(this.simpleRenderers.length){const r=new S(n);return r.simpleRenderers=this.simpleRenderers,r.parse(t,e)}return S.parse(e,t,n)}static callMe(e){if(e.message+="\nPlease report this to https://github.com/ts-stack/markdown",this.options.silent)return"<p>An error occured:</p><pre>"+this.options.escape(e.message+"",!0)+"</pre>";throw e}}class E{staticThis;static simpleRules=[];static rulesBase=null;static rulesGfm=null;static rulesTables=null;rules;options;links={};tokens=[];hasRulesGfm;hasRulesTables;constructor(e,t){this.staticThis=e,this.options=t||j.options,this.setRules()}static lex(e,t,n,r){return new this(this,t).getTokens(e,n,r)}static getRulesBase(){if(this.rulesBase)return this.rulesBase;const e={newline:/^\n+/,code:/^( {4}[^\n]+\n*)+/,hr:/^( *[-*_]){3,} *(?:\n+|$)/,heading:/^ *(#{1,6}) *([^\n]+?) *#* *(?:\n+|$)/,lheading:/^([^\n]+)\n *(=|-){2,} *(?:\n+|$)/,blockquote:/^( *>[^\n]+(\n[^\n]+)*\n*)+/,list:/^( *)(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?!\1bull )\n*|\s*$)/,html:/^ *(?:comment *(?:\n|\s*$)|closed *(?:\n{2,}|\s*$)|closing *(?:\n{2,}|\s*$))/,def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +["(]([^\n]+)[")])? *(?:\n+|$)/,paragraph:/^((?:[^\n]+\n?(?!hr|heading|lheading|blockquote|tag|def))+)\n*/,text:/^[^\n]+/,bullet:/(?:[*+-]|\d+\.)/,item:/^( *)(bull) [^\n]*(?:\n(?!\1bull )[^\n]*)*/};e.item=new d(e.item,"gm").setGroup(/bull/g,e.bullet).getRegexp(),e.list=new d(e.list).setGroup(/bull/g,e.bullet).setGroup("hr","\\n+(?=\\1?(?:[-*_] *){3,}(?:\\n+|$))").setGroup("def","\\n+(?="+e.def.source+")").getRegexp();const t="(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:/|[^\\w\\s@]*@)\\b";return e.html=new d(e.html).setGroup("comment",/<!--[\s\S]*?-->/).setGroup("closed",/<(tag)[\s\S]+?<\/\1>/).setGroup("closing",/<tag(?:"[^"]*"|'[^']*'|[^'">])*?>/).setGroup(/tag/g,t).getRegexp(),e.paragraph=new d(e.paragraph).setGroup("hr",e.hr).setGroup("heading",e.heading).setGroup("lheading",e.lheading).setGroup("blockquote",e.blockquote).setGroup("tag","<"+t).setGroup("def",e.def).getRegexp(),this.rulesBase=e}static getRulesGfm(){if(this.rulesGfm)return this.rulesGfm;const e=this.getRulesBase(),t={...e,fences:/^ *(`{3,}|~{3,})[ \.]*((\S+)? *[^\n]*)\n([\s\S]*?)\s*\1 *(?:\n+|$)/,paragraph:/^/,heading:/^ *(#{1,6}) +([^\n]+?) *#* *(?:\n+|$)/},n=t.fences.source.replace("\\1","\\2"),r=e.list.source.replace("\\1","\\3");return t.paragraph=new d(e.paragraph).setGroup("(?!",`(?!${n}|${r}|`).getRegexp(),this.rulesGfm=t}static getRulesTable(){return this.rulesTables?this.rulesTables:this.rulesTables={...this.getRulesGfm(),nptable:/^ *(\S.*\|.*)\n *([-:]+ *\|[-| :]*)\n((?:.*\|.*(?:\n|$))*)\n*/,table:/^ *\|(.+)\n *\|( *[-:]+[-| :]*)\n((?: *\|.*(?:\n|$))*)\n*/}}setRules(){this.options.gfm?this.options.tables?this.rules=this.staticThis.getRulesTable():this.rules=this.staticThis.getRulesGfm():this.rules=this.staticThis.getRulesBase(),this.hasRulesGfm=void 0!==this.rules.fences,this.hasRulesTables=void 0!==this.rules.table}getTokens(e,t,n){let r,o=e;e:for(;o;)if((r=this.rules.newline.exec(o))&&(o=o.substring(r[0].length),r[0].length>1&&this.tokens.push({type:v.space})),r=this.rules.code.exec(o)){o=o.substring(r[0].length);const e=r[0].replace(/^ {4}/gm,"");this.tokens.push({type:v.code,text:this.options.pedantic?e:e.replace(/\n+$/,"")})}else if(this.hasRulesGfm&&(r=this.rules.fences.exec(o)))o=o.substring(r[0].length),this.tokens.push({type:v.code,meta:r[2],lang:r[3],text:r[4]||""});else if(r=this.rules.heading.exec(o))o=o.substring(r[0].length),this.tokens.push({type:v.heading,depth:r[1].length,text:r[2]});else if(t&&this.hasRulesTables&&(r=this.rules.nptable.exec(o))){o=o.substring(r[0].length);const e={type:v.table,header:r[1].replace(/^ *| *\| *$/g,"").split(/ *\| */),align:r[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:[]};for(let n=0;n<e.align.length;n++)/^ *-+: *$/.test(e.align[n])?e.align[n]="right":/^ *:-+: *$/.test(e.align[n])?e.align[n]="center":/^ *:-+ *$/.test(e.align[n])?e.align[n]="left":e.align[n]=null;const t=r[3].replace(/\n$/,"").split("\n");for(let n=0;n<t.length;n++)e.cells[n]=t[n].split(/ *\| */);this.tokens.push(e)}else if(r=this.rules.lheading.exec(o))o=o.substring(r[0].length),this.tokens.push({type:v.heading,depth:"="===r[2]?1:2,text:r[1]});else if(r=this.rules.hr.exec(o))o=o.substring(r[0].length),this.tokens.push({type:v.hr});else if(r=this.rules.blockquote.exec(o)){o=o.substring(r[0].length),this.tokens.push({type:v.blockquoteStart});const e=r[0].replace(/^ *> ?/gm,"");this.getTokens(e),this.tokens.push({type:v.blockquoteEnd})}else if(r=this.rules.list.exec(o)){o=o.substring(r[0].length);const e=r[2];this.tokens.push({type:v.listStart,ordered:e.length>1});const t=r[0].match(this.rules.item),a=t.length;let i,l,s,u=!1;for(let r=0;r<a;r++){let c=t[r];i=c.length,c=c.replace(/^ *([*+-]|\d+\.) +/,""),-1!==c.indexOf("\n ")&&(i-=c.length,c=this.options.pedantic?c.replace(/^ {1,4}/gm,""):c.replace(new RegExp("^ {1,"+i+"}","gm"),"")),this.options.smartLists&&r!==a-1&&(l=this.staticThis.getRulesBase().bullet.exec(t[r+1])[0],e===l||e.length>1&&l.length>1||(o=t.slice(r+1).join("\n")+o,r=a-1)),s=u||/\n\n(?!\s*$)/.test(c),r!==a-1&&(u="\n"===c.charAt(c.length-1),s||(s=u)),this.tokens.push({type:s?v.looseItemStart:v.listItemStart}),this.getTokens(c,!1,n),this.tokens.push({type:v.listItemEnd})}this.tokens.push({type:v.listEnd})}else if(r=this.rules.html.exec(o)){o=o.substring(r[0].length);const e=r[1],t="pre"===e||"script"===e||"style"===e;this.tokens.push({type:this.options.sanitize?v.paragraph:v.html,pre:!this.options.sanitizer&&t,text:r[0]})}else if(t&&(r=this.rules.def.exec(o)))o=o.substring(r[0].length),this.links[r[1].toLowerCase()]={href:r[2],title:r[3]};else if(t&&this.hasRulesTables&&(r=this.rules.table.exec(o))){o=o.substring(r[0].length);const e={type:v.table,header:r[1].replace(/^ *| *\| *$/g,"").split(/ *\| */),align:r[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:[]};for(let n=0;n<e.align.length;n++)/^ *-+: *$/.test(e.align[n])?e.align[n]="right":/^ *:-+: *$/.test(e.align[n])?e.align[n]="center":/^ *:-+ *$/.test(e.align[n])?e.align[n]="left":e.align[n]=null;const t=r[3].replace(/(?: *\| *)?\n$/,"").split("\n");for(let n=0;n<t.length;n++)e.cells[n]=t[n].replace(/^ *\| *| *\| *$/g,"").split(/ *\| */);this.tokens.push(e)}else{if(this.staticThis.simpleRules.length){const e=this.staticThis.simpleRules;for(let t=0;t<e.length;t++)if(r=e[t].exec(o)){o=o.substring(r[0].length);const e="simpleRule"+(t+1);this.tokens.push({type:e,execArr:r});continue e}}if(t&&(r=this.rules.paragraph.exec(o)))o=o.substring(r[0].length),"\n"===r[1].slice(-1)?this.tokens.push({type:v.paragraph,text:r[1].slice(0,-1)}):this.tokens.push({type:this.tokens.length>0?v.paragraph:v.text,text:r[1]});else if(r=this.rules.text.exec(o))o=o.substring(r[0].length),this.tokens.push({type:v.text,text:r[0]});else if(o)throw new Error("Infinite loop on byte: "+o.charCodeAt(0)+`, near text '${o.slice(0,30)}...'`)}return{tokens:this.tokens,links:this.links}}}const C="hotkey",_="Control+F11";class N extends Error{constructor(e,t,n){super(e),this.code=t,this.statusCode=n,this.name="AppError"}}const P=async(e,t)=>{try{var n;const r=await chrome.storage.local.get([e]);return(e=>{if(chrome.runtime.lastError)throw console.error(`Chrome ${e} error:`,chrome.runtime.lastError),new N(`Chrome extension error during ${e}: ${chrome.runtime.lastError.message}`,"CHROME_ERROR")})("storage.get"),null!==(n=r[e])&&void 0!==n?n:t}catch(r){return((e,t,n)=>{console.group(`\ud83d\udea8 Error in ${t}`),console.error("Error:",e),console.error("Stack:",e.stack),n&&console.error("Additional data:",n),console.groupEnd()})(r,`getStorageItem(${e})`),t}};var T=n(579);const L=()=>{const[e,n]=(0,r.useState)(),[o,a]=(0,r.useState)(!1),[i,l]=(0,r.useState)(!1),[s,c]=(0,r.useState)(!1),d=(0,r.useCallback)((async()=>{try{a(!0);const e=await u();if(console.log("success",e),"success"in e&&!1===e.success)return c(!0),void f(e.message||"Failed to fetch job details","error");"companyName"in e&&n(e)}catch(e){console.error("Error parsing job details:",e),f("Failed to fetch job details","error"),c(!0)}finally{a(!1)}}),[]);(0,r.useEffect)((()=>{d()}),[d]);const f=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";window.showJobTrackerToast(e,t)};(0,r.useEffect)((()=>{const e=(e,t,n)=>{console.log("Content script received message:",e),"SHOW_DIALOG"===e.type&&(console.log("Showing dialog..."),z(),n({success:!0}))};return chrome.runtime.onMessage.addListener(e),()=>{chrome.runtime.onMessage.removeListener(e)}}),[]);const p=()=>{const e=document.getElementById("job-tracker-dialog-container");e&&e.remove()};return(0,T.jsx)(t(),{handle:".job-tracker-handle",cancel:"input, textarea, button, select",children:(0,T.jsxs)("div",{className:"job-tracker-dialog",children:[(0,T.jsxs)("div",{className:"job-tracker-dialog-header",children:[(0,T.jsxs)("div",{className:"job-tracker-handle",children:[(0,T.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})}),"Job Application Tracker"]}),(0,T.jsx)("button",{onClick:p,className:"job-tracker-close","aria-label":"Close dialog",children:(0,T.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,T.jsx)("div",{className:"job-tracker-dialog-content",children:o?(0,T.jsx)("div",{className:"loading-state",style:{padding:"40px 20px",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"200px",background:"transparent"},children:(0,T.jsxs)("div",{className:"loading-content",style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",gap:"16px",background:"transparent"},children:[(0,T.jsx)("div",{className:"loading-spinner",ref:e=>{if(e){const t="job-tracker-spinner-animation";if(!document.getElementById(t)){const e=document.createElement("style");e.id=t,e.textContent="\n                                                    @keyframes job-tracker-spin {\n                                                        from { transform: rotate(0deg); }\n                                                        to { transform: rotate(360deg); }\n                                                    }\n                                                ",document.head.appendChild(e)}e.setAttribute("style","\n                                                width: 40px !important;\n                                                height: 40px !important;\n                                                border: 3px solid #e5e7eb !important;\n                                                border-top: 3px solid #3b82f6 !important;\n                                                border-radius: 50% !important;\n                                                animation: job-tracker-spin 1s linear infinite !important;\n                                                margin: 0 auto !important;\n                                                padding: 0 !important;\n                                                background: transparent !important;\n                                                display: block !important;\n                                                opacity: 1 !important;\n                                                visibility: visible !important;\n                                                box-sizing: border-box !important;\n                                                position: relative !important;\n                                                z-index: 1 !important;\n                                                min-width: 40px !important;\n                                                min-height: 40px !important;\n                                                max-width: 40px !important;\n                                                max-height: 40px !important;\n                                                flex-shrink: 0 !important;\n                                            ")}}}),(0,T.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",margin:"0",padding:"0",textAlign:"center",lineHeight:"1.4",background:"transparent",border:"none"},children:"Extracting Job Details"}),(0,T.jsx)("p",{style:{color:"#6b7280",fontSize:"14px",margin:"0",padding:"0",textAlign:"center",lineHeight:"1.4",background:"transparent",border:"none"},children:"Please wait while we analyze the job posting..."})]})}):e?(0,T.jsxs)("div",{children:[(0,T.jsxs)("div",{className:"form-field",children:[(0,T.jsxs)("label",{className:"input-label",children:[(0,T.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",style:{display:"inline",marginRight:"8px"},children:(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m-8 0V6a2 2 0 00-2 2v6"})}),"Job Title"]}),(0,T.jsx)("input",{className:"input-field",type:"text",value:e.jobRole,onChange:t=>n({...e,jobRole:t.target.value}),placeholder:"Enter job title"})]}),(0,T.jsxs)("div",{className:"form-field",children:[(0,T.jsxs)("label",{className:"input-label",children:[(0,T.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",style:{display:"inline",marginRight:"8px"},children:(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})}),"Company"]}),(0,T.jsx)("input",{className:"input-field",type:"text",value:e.companyName,onChange:t=>n({...e,companyName:t.target.value}),placeholder:"Enter company name"})]}),((null===e||void 0===e?void 0:e.companyNationality)||(null===e||void 0===e?void 0:e.companySite))&&(0,T.jsxs)("div",{className:"info-grid",children:[(null===e||void 0===e?void 0:e.companyNationality)&&(0,T.jsxs)("div",{className:"info-card",children:[(0,T.jsx)("div",{className:"info-card-label",children:"Company Nationality"}),(0,T.jsx)("div",{className:"info-card-value",children:e.companyNationality})]}),(null===e||void 0===e?void 0:e.companySite)&&(0,T.jsxs)("div",{className:"info-card",children:[(0,T.jsx)("div",{className:"info-card-label",children:"Company Site"}),(0,T.jsx)("div",{className:"info-card-value",children:e.companySite})]})]}),e.remoteAvailability&&(0,T.jsxs)("div",{className:"remote-highlight",children:[(0,T.jsxs)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("div",{className:"remote-label",children:"Remote Availability"}),(0,T.jsx)("div",{className:"remote-value",children:e.remoteAvailability})]})]}),(0,T.jsxs)("div",{className:"content-section",children:[(0,T.jsxs)("div",{className:"section-label",children:[(0,T.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})}),"Tech Stack"]}),e.techStacks.length>0?(0,T.jsx)("div",{className:"tech-stack-container",children:e.techStacks.map(((e,t)=>(0,T.jsx)("span",{className:"tech-chip",children:e},t)))}):(0,T.jsx)("div",{className:"no-content",children:(0,T.jsx)("p",{children:"No tech stack detected"})})]}),(0,T.jsxs)("div",{className:"content-section",children:[(0,T.jsxs)("div",{className:"section-label",children:[(0,T.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Job Description"]}),(0,T.jsx)("div",{className:"job-description",children:(0,T.jsx)("div",{className:"prose",dangerouslySetInnerHTML:{__html:j.parse(e.jobDescription||"No description available")}})})]})]}):(0,T.jsxs)("div",{style:{textAlign:"center",padding:"40px 20px"},children:[(0,T.jsx)("div",{style:{background:"linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)",padding:"16px",borderRadius:"50%",display:"inline-flex",alignItems:"center",justifyContent:"center",marginBottom:"16px",border:"1px solid #fecaca"},children:(0,T.jsx)("svg",{style:{width:"32px",height:"32px",color:"#dc2626"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,T.jsx)("h3",{style:{fontSize:"18px",fontWeight:"600",color:"#111827",marginBottom:"8px"},children:"Failed to Extract Job Details"}),(0,T.jsx)("p",{style:{color:"#6b7280",marginBottom:"24px"},children:"We couldn't automatically extract the job information from this page."}),(0,T.jsxs)("div",{style:{display:"flex",gap:"12px",justifyContent:"center"},children:[s&&(0,T.jsxs)("button",{onClick:d,className:"btn btn-outline",children:[(0,T.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Retry"]}),(0,T.jsx)("button",{onClick:p,className:"btn btn-primary",children:"Close"})]})]})}),e&&(0,T.jsx)("div",{className:"save-button-container",children:(0,T.jsx)("button",{onClick:async()=>{if(e)try{l(!0),chrome.runtime.sendMessage({type:"SAVE_JOB",payload:e},(e=>{console.log(e),e.success?(f("Job application saved successfully!","success"),setTimeout((()=>{p()}),1500)):f(e.message||"Failed to save job application","error")}))}catch(t){console.error("Error saving job:",t),f("Failed to save job. Please try again.","error")}finally{l(!1)}},disabled:i,className:"save-button",children:i?(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("div",{className:"job-tracker-toast-spinner",style:{width:"16px",height:"16px",borderWidth:"2px"}}),"Saving Application..."]}):(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,T.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"})}),"Save Job Application"]})})})]})})},z=async()=>{const e=document.getElementById("job-tracker-dialog-container");e&&e.remove(),await D();const t=document.createElement("div");t.id="job-tracker-dialog-container",document.body.appendChild(t);(0,c.createRoot)(t).render((0,T.jsx)(L,{}))},D=async()=>{let e="light";try{e=(await chrome.storage.local.get(["theme"])).theme||"light"}catch(r){console.error("Error loading theme:",r)}const t=`\n    /* Job Tracker Extension Styles - Isolated */\n    #job-tracker-dialog-container {\n        position: fixed;\n        top: 100px;\n        right: 100px;\n        z-index: 999999;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        font-size: 14px;\n        line-height: 1.5;\n        color: ${"dark"===e?"#f9fafb":"#111827"};\n    }\n\n    /* Minimal universal reset - only the most critical properties */\n    #job-tracker-dialog-container *,\n    #job-tracker-dialog-container *::before,\n    #job-tracker-dialog-container *::after {\n        box-sizing: border-box !important;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;\n    }\n\n    /* Reset only problematic inherited styles */\n    #job-tracker-dialog-container {\n        line-height: 1.5 !important;\n        color: ${"dark"===e?"#f9fafb":"#111827"} !important;\n        font-size: 14px !important;\n        text-decoration: none !important;\n        text-transform: none !important;\n        letter-spacing: normal !important;\n        word-spacing: normal !important;\n        text-indent: 0 !important;\n        text-shadow: none !important;\n        text-align: left !important;\n        white-space: normal !important;\n        list-style: none !important;\n    }\n    /* Dialog container */\n    #job-tracker-dialog-container .job-tracker-dialog {\n        border-radius: 12px !important;\n        background: ${"dark"===e?"#1f2937":"white"} !important;\n        border: 1px solid ${"dark"===e?"#374151":"#e5e7eb"} !important;\n        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, ${"dark"===e?"0.3":"0.1"}), 0 10px 10px -5px rgba(0, 0, 0, ${"dark"===e?"0.2":"0.04"}) !important;\n        width: 520px !important;\n        max-width: 95vw !important;\n        max-height: 85vh !important;\n        overflow: hidden !important;\n        animation: slideIn 0.3s ease-out !important;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;\n        display: flex !important;\n        flex-direction: column !important;\n        position: relative !important;\n        z-index: 999999 !important;\n        margin: 0 !important;\n        padding: 0 !important;\n        line-height: 1.5 !important;\n        font-size: 14px !important;\n        color: ${"dark"===e?"#f9fafb":"#111827"} !important;\n    }\n\n    /* Selective resets to prevent host page interference while preserving dialog functionality */\n    #job-tracker-dialog-container div:not(.job-tracker-dialog):not(.job-tracker-dialog-header):not(.job-tracker-dialog-content):not(.save-button-container):not(.remote-highlight):not(.info-grid):not(.content-section):not(.tech-stack-container):not(.job-description):not(.no-content):not(.loading-state):not(.loading-content) {\n        line-height: 1.5 !important;\n        margin: 0 !important;\n        padding: 0 !important;\n        background: transparent !important;\n        border: none !important;\n        text-decoration: none !important;\n        text-transform: none !important;\n        text-shadow: none !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n        float: none !important;\n        clear: none !important;\n    }\n\n    /* Protect text elements from host page styles */\n    #job-tracker-dialog-container span,\n    #job-tracker-dialog-container p,\n    #job-tracker-dialog-container h1,\n    #job-tracker-dialog-container h2,\n    #job-tracker-dialog-container h3,\n    #job-tracker-dialog-container label {\n        line-height: 1.5 !important;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;\n        text-decoration: none !important;\n        text-transform: none !important;\n        letter-spacing: normal !important;\n        word-spacing: normal !important;\n        text-indent: 0 !important;\n        text-shadow: none !important;\n        font-weight: normal !important;\n        font-style: normal !important;\n        text-align: inherit !important;\n    }\n\n    /* Protect form elements */\n    #job-tracker-dialog-container input,\n    #job-tracker-dialog-container button {\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;\n        line-height: 1.5 !important;\n        text-decoration: none !important;\n        text-transform: none !important;\n        letter-spacing: normal !important;\n        appearance: none !important;\n        outline: none !important;\n    }\n\n    @keyframes slideIn {\n        from {\n            opacity: 0;\n            transform: translateY(-20px) scale(0.95);\n        }\n        to {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n        }\n    }\n\n    @keyframes spin {\n        from {\n            transform: rotate(0deg);\n        }\n        to {\n            transform: rotate(360deg);\n        }\n    }\n\n    /* Dialog header */\n    #job-tracker-dialog-container .job-tracker-dialog-header {\n        background: linear-gradient(135deg, ${"dark"===e?"#1e40af":"#3b82f6"} 0%, ${"dark"===e?"#1e3a8a":"#2563eb"} 100%);\n        color: white;\n        padding: 16px 20px;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        cursor: move;\n        user-select: none;\n    }\n\n    /* Drag handle */\n    #job-tracker-dialog-container .job-tracker-handle {\n        display: flex;\n        align-items: center;\n        flex: 1;\n        font-weight: 600;\n        font-size: 16px;\n    }\n\n    /* Close button */\n    #job-tracker-dialog-container .job-tracker-close {\n        background: none;\n        border: none;\n        color: white;\n        cursor: pointer;\n        padding: 8px;\n        border-radius: 6px;\n        transition: background-color 0.2s;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 32px;\n        height: 32px;\n    }\n\n    #job-tracker-dialog-container .job-tracker-close:hover {\n        background-color: rgba(255, 255, 255, 0.2);\n    }\n\n    /* Dialog content */\n    #job-tracker-dialog-container .job-tracker-dialog-content {\n        padding: 16px 20px;\n        flex: 1;\n        overflow-y: auto;\n    }\n\n    /* Form field */\n    #job-tracker-dialog-container .form-field {\n        display: flex;\n        flex-direction: column;\n        gap: 6px;\n        margin-bottom: 16px;\n    }\n\n    /* Label */\n    #job-tracker-dialog-container .input-label {\n        font-size: 14px;\n        font-weight: 600;\n        color: #374151;\n        margin-bottom: 6px;\n        display: flex;\n        align-items: center;\n        gap: 8px;\n    }\n\n    /* Icon styling */\n    #job-tracker-dialog-container .input-label svg,\n    #job-tracker-dialog-container .job-tracker-handle svg,\n    #job-tracker-dialog-container .job-tracker-close svg,\n    #job-tracker-dialog-container .btn svg,\n    #job-tracker-dialog-container .save-button svg {\n        width: 16px;\n        height: 16px;\n        flex-shrink: 0;\n    }\n\n    #job-tracker-dialog-container .job-tracker-handle svg {\n        width: 20px;\n        height: 20px;\n    }\n\n    /* Input field */\n    #job-tracker-dialog-container .input-field {\n        padding: 12px 16px;\n        border-radius: 8px;\n        border: 1px solid ${"dark"===e?"#4b5563":"#d1d5db"};\n        transition: all 0.2s ease;\n        outline: none;\n        font-size: 14px;\n        background: ${"dark"===e?"#374151":"white"};\n        color: ${"dark"===e?"#f9fafb":"#111827"};\n    }\n\n    #job-tracker-dialog-container .input-field:focus {\n        border-color: #3b82f6;\n        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n    }\n\n    #job-tracker-dialog-container .input-field::placeholder {\n        color: #9ca3af;\n    }\n\n    /* Textarea */\n    #job-tracker-dialog-container textarea.input-field {\n        resize: vertical;\n        min-height: 80px;\n    }\n\n    /* Select */\n    #job-tracker-dialog-container select.input-field {\n        cursor: pointer;\n        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");\n        background-position: right 12px center;\n        background-repeat: no-repeat;\n        background-size: 16px;\n        padding-right: 40px;\n    }\n\n    /* Button Styles */\n    #job-tracker-dialog-container .btn {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 8px;\n        font-weight: 600;\n        transition: all 0.2s ease-in-out;\n        cursor: pointer;\n        border: none;\n        text-decoration: none;\n        font-size: 14px;\n        line-height: 1.25;\n        gap: 8px;\n        padding: 12px 20px;\n        outline: none;\n    }\n\n    #job-tracker-dialog-container .btn:focus {\n        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);\n    }\n\n    #job-tracker-dialog-container .btn-primary {\n        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);\n        color: white;\n    }\n\n    #job-tracker-dialog-container .btn-primary:hover {\n        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\n        transform: translateY(-1px);\n        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\n    }\n\n    #job-tracker-dialog-container .btn-success {\n        background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n        color: white;\n    }\n\n    #job-tracker-dialog-container .btn-success:hover {\n        background: linear-gradient(135deg, #059669 0%, #047857 100%);\n        transform: translateY(-1px);\n        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);\n    }\n\n    #job-tracker-dialog-container .btn-outline {\n        background: white;\n        border: 1px solid #d1d5db;\n        color: #374151;\n    }\n\n    #job-tracker-dialog-container .btn-outline:hover {\n        background: #f9fafb;\n        border-color: #9ca3af;\n    }\n\n    #job-tracker-dialog-container .btn-full {\n        width: 100%;\n    }\n\n    #job-tracker-dialog-container .btn:disabled {\n        opacity: 0.5;\n        cursor: not-allowed;\n        transform: none !important;\n    }\n\n    /* Save button container - sticky at bottom */\n    #job-tracker-dialog-container .save-button-container {\n        position: sticky;\n        bottom: 0;\n        left: 0;\n        right: 0;\n        border-top: 1px solid ${"dark"===e?"#374151":"#e5e7eb"};\n        padding: 16px 20px;\n        background: linear-gradient(135deg, ${"dark"===e?"#374151":"#f9fafb"} 0%, ${"dark"===e?"#4b5563":"#f3f4f6"} 100%);\n        backdrop-filter: blur(8px);\n        z-index: 10;\n        box-shadow: 0 -2px 8px rgba(0, 0, 0, ${"dark"===e?"0.3":"0.1"});\n    }\n\n    /* Save button specific */\n    #job-tracker-dialog-container .save-button {\n        width: 100%;\n        background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n        color: white;\n        padding: 14px 24px;\n        border-radius: 8px;\n        font-weight: 600;\n        transition: all 0.2s ease;\n        border: none;\n        cursor: pointer;\n        font-size: 14px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 8px;\n        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);\n    }\n\n    #job-tracker-dialog-container .save-button:hover:not(:disabled) {\n        background: linear-gradient(135deg, #059669 0%, #047857 100%);\n        transform: translateY(-1px);\n        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);\n    }\n\n    #job-tracker-dialog-container .save-button:disabled {\n        opacity: 0.7;\n        cursor: not-allowed;\n        transform: none;\n    }\n\n    /* Content sections */\n    #job-tracker-dialog-container .content-section {\n        margin-bottom: 18px;\n    }\n\n    #job-tracker-dialog-container .section-label {\n        font-size: 14px;\n        font-weight: 600;\n        color: #374151;\n        margin-bottom: 8px;\n        display: flex;\n        align-items: center;\n        gap: 6px;\n    }\n\n    #job-tracker-dialog-container .section-label svg {\n        width: 14px;\n        height: 14px;\n        flex-shrink: 0;\n    }\n\n    #job-tracker-dialog-container .info-grid {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        gap: 12px;\n        margin-bottom: 16px;\n    }\n\n    #job-tracker-dialog-container .info-card {\n        background: #f9fafb;\n        border: 1px solid #e5e7eb;\n        border-radius: 8px;\n        padding: 12px;\n    }\n\n    #job-tracker-dialog-container .info-card-label {\n        font-size: 11px;\n        font-weight: 600;\n        color: #6b7280;\n        text-transform: uppercase;\n        letter-spacing: 0.05em;\n        margin-bottom: 4px;\n    }\n\n    #job-tracker-dialog-container .info-card-value {\n        font-size: 14px;\n        font-weight: 500;\n        color: #111827;\n    }\n\n    /* Remote availability highlight */\n    #job-tracker-dialog-container .remote-highlight {\n        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\n        border: 1px solid #93c5fd;\n        border-radius: 8px;\n        padding: 12px;\n        margin-bottom: 16px;\n        display: flex;\n        align-items: center;\n        gap: 10px;\n    }\n\n    #job-tracker-dialog-container .remote-highlight svg {\n        width: 20px;\n        height: 20px;\n        color: #2563eb;\n        flex-shrink: 0;\n    }\n\n    #job-tracker-dialog-container .remote-highlight .remote-label {\n        font-size: 11px;\n        font-weight: 600;\n        color: #1e40af;\n        text-transform: uppercase;\n        letter-spacing: 0.05em;\n        margin-bottom: 2px;\n    }\n\n    #job-tracker-dialog-container .remote-highlight .remote-value {\n        font-size: 14px;\n        font-weight: 600;\n        color: #1e3a8a;\n    }\n\n    /* Tech stack chips */\n    #job-tracker-dialog-container .tech-stack-container {\n        display: flex;\n        flex-wrap: wrap;\n        gap: 8px;\n        margin-top: 8px;\n    }\n\n    #job-tracker-dialog-container .tech-chip {\n        display: inline-flex;\n        align-items: center;\n        padding: 6px 12px;\n        border-radius: 20px;\n        font-size: 12px;\n        font-weight: 500;\n        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\n        color: #1e40af;\n        border: 1px solid #93c5fd;\n        transition: all 0.2s ease;\n    }\n\n    #job-tracker-dialog-container .tech-chip:hover {\n        background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);\n        transform: translateY(-1px);\n    }\n\n    /* No content placeholder */\n    #job-tracker-dialog-container .no-content {\n        background: #f9fafb;\n        border: 1px solid #e5e7eb;\n        border-radius: 8px;\n        padding: 20px;\n        text-align: center;\n        color: #6b7280;\n        font-size: 14px;\n    }\n\n    /* Job description */\n    #job-tracker-dialog-container .job-description {\n        background: ${"dark"===e?"#374151":"#f9fafb"};\n        border: 1px solid ${"dark"===e?"#4b5563":"#e5e7eb"};\n        border-radius: 8px;\n        padding: 12px;\n        max-height: 150px;\n        overflow-y: auto;\n    }\n\n    #job-tracker-dialog-container .job-description .prose {\n        font-size: 14px;\n        line-height: 1.6;\n        color: #374151;\n    }\n\n    #job-tracker-dialog-container .job-description .prose h1,\n    #job-tracker-dialog-container .job-description .prose h2,\n    #job-tracker-dialog-container .job-description .prose h3 {\n        font-weight: 600;\n        margin-bottom: 8px;\n        color: #111827;\n    }\n\n    #job-tracker-dialog-container .job-description .prose p {\n        margin-bottom: 12px;\n    }\n\n    #job-tracker-dialog-container .job-description .prose ul,\n    #job-tracker-dialog-container .job-description .prose ol {\n        margin-left: 20px;\n        margin-bottom: 12px;\n    }\n\n    #job-tracker-dialog-container .job-description .prose li {\n        margin-bottom: 4px;\n    }\n\n    /* Account item */\n    #job-tracker-dialog-container .account-item {\n        padding: 12px;\n        border-radius: 8px;\n        border: 1px solid #e5e7eb;\n        margin-bottom: 8px;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        transition: all 0.2s ease;\n        cursor: pointer;\n        background: white;\n    }\n\n    #job-tracker-dialog-container .account-item:hover {\n        background-color: #f9fafb;\n        border-color: #d1d5db;\n    }\n\n    #job-tracker-dialog-container .account-item.selected {\n        border-color: #3b82f6;\n        background-color: #eff6ff;\n    }\n\n    /* Toast notification */\n    #job-tracker-dialog-container .toast {\n        position: fixed;\n        bottom: 20px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n        color: white;\n        padding: 12px 20px;\n        border-radius: 8px;\n        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);\n        z-index: 999999;\n        font-size: 14px;\n        font-weight: 500;\n    }\n\n/* Animations */\n@keyframes fade-in {\n    from {\n        opacity: 0;\n        transform: translateY(10px);\n    }\n\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n.animate-fade-in {\n    animation: fade-in 0.3s ease-out;\n}\n\n    /* Toast Notifications - Global styles with maximum specificity */\n    body .job-tracker-toast,\n    html .job-tracker-toast,\n    div.job-tracker-toast {\n        position: fixed !important;\n        top: 20px !important;\n        right: 20px !important;\n        z-index: 2147483647 !important; /* Maximum z-index value */\n        min-width: 300px !important;\n        max-width: 400px !important;\n        padding: 16px !important;\n        border-radius: 8px !important;\n        color: white !important;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;\n        font-size: 14px !important;\n        font-weight: normal !important;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;\n        transform: translateX(calc(100% + 20px)) !important;\n        transition: transform 0.3s ease-in-out !important;\n        display: flex !important;\n        align-items: center !important;\n        gap: 12px !important;\n        margin: 0 !important;\n        border: none !important;\n        outline: none !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n        pointer-events: auto !important;\n        overflow: visible !important;\n        clip: auto !important;\n        filter: none !important;\n        width: auto !important;\n        height: auto !important;\n        min-height: auto !important;\n        max-height: none !important;\n        left: auto !important;\n        bottom: auto !important;\n        float: none !important;\n        clear: none !important;\n        vertical-align: baseline !important;\n        text-decoration: none !important;\n        text-transform: none !important;\n        letter-spacing: normal !important;\n        word-spacing: normal !important;\n        text-indent: 0 !important;\n        text-shadow: none !important;\n        white-space: normal !important;\n        word-wrap: normal !important;\n        word-break: normal !important;\n        hyphens: none !important;\n        user-select: auto !important;\n        cursor: auto !important;\n        resize: none !important;\n        appearance: none !important;\n        box-sizing: border-box !important;\n        line-height: 1.4 !important;\n        text-align: left !important;\n    }\n\n    body .job-tracker-toast.show,\n    html .job-tracker-toast.show,\n    div.job-tracker-toast.show {\n        transform: translateX(0) !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n    }\n\n    body .job-tracker-toast.info,\n    html .job-tracker-toast.info,\n    div.job-tracker-toast.info {\n        background-color: #3b82f6 !important;\n        background: #3b82f6 !important;\n    }\n\n    body .job-tracker-toast.success,\n    html .job-tracker-toast.success,\n    div.job-tracker-toast.success {\n        background-color: #10b981 !important;\n        background: #10b981 !important;\n    }\n\n    body .job-tracker-toast.error,\n    html .job-tracker-toast.error,\n    div.job-tracker-toast.error {\n        background-color: #ef4444 !important;\n        background: #ef4444 !important;\n    }\n\n    body .job-tracker-toast.loading,\n    html .job-tracker-toast.loading,\n    div.job-tracker-toast.loading {\n        background-color: #6366f1 !important;\n        background: #6366f1 !important;\n    }\n\n    .job-tracker-toast .job-tracker-toast-icon {\n        flex-shrink: 0 !important;\n        width: 20px !important;\n        height: 20px !important;\n        display: block !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n    }\n\n    .job-tracker-toast .job-tracker-toast-spinner {\n        width: 20px !important;\n        height: 20px !important;\n        border: 2px solid rgba(255, 255, 255, 0.3) !important;\n        border-top: 2px solid white !important;\n        border-radius: 50% !important;\n        animation: job-tracker-spin 1s linear infinite !important;\n        display: block !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n        flex-shrink: 0 !important;\n    }\n\n    .job-tracker-toast .job-tracker-toast-content {\n        flex: 1 !important;\n        display: block !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n    }\n\n    .job-tracker-toast .job-tracker-toast-title {\n        font-weight: 600 !important;\n        margin-bottom: 4px !important;\n        color: white !important;\n        display: block !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n        font-size: 14px !important;\n        line-height: 1.4 !important;\n    }\n\n    .job-tracker-toast .job-tracker-toast-message {\n        opacity: 0.9 !important;\n        font-size: 13px !important;\n        color: white !important;\n        display: block !important;\n        visibility: visible !important;\n        line-height: 1.4 !important;\n        margin: 0 !important;\n        padding: 0 !important;\n    }\n\n    /* Responsive */\n    @media (max-width: 768px) {\n        #job-tracker-dialog-container .job-tracker-dialog {\n            width: 95vw;\n            max-width: none;\n            margin: 10px;\n        }\n\n        #job-tracker-dialog-container .job-tracker-dialog-content {\n            padding: 16px;\n        }\n\n        #job-tracker-dialog-container .job-tracker-dialog-header {\n            padding: 12px 16px;\n        }\n\n        .job-tracker-toast {\n            right: 10px !important;\n            left: 10px !important;\n            min-width: auto !important;\n            max-width: none !important;\n        }\n    }\n\n    /* Loading state - with high specificity to override resets */\n    #job-tracker-dialog-container .loading-state {\n        padding: 40px 20px !important;\n        text-align: center !important;\n        display: flex !important;\n        flex-direction: column !important;\n        align-items: center !important;\n        justify-content: center !important;\n        min-height: 200px !important;\n        background: transparent !important;\n    }\n\n    #job-tracker-dialog-container .loading-content {\n        display: flex !important;\n        flex-direction: column !important;\n        align-items: center !important;\n        justify-content: center !important;\n        gap: 16px !important;\n        background: transparent !important;\n    }\n\n    #job-tracker-dialog-container .loading-content h3 {\n        font-size: 18px !important;\n        font-weight: 600 !important;\n        color: #111827 !important;\n        margin: 0 !important;\n        padding: 0 !important;\n        text-align: center !important;\n        line-height: 1.4 !important;\n        background: transparent !important;\n        border: none !important;\n    }\n\n    #job-tracker-dialog-container .loading-content p {\n        color: #6b7280 !important;\n        font-size: 14px !important;\n        margin: 0 !important;\n        padding: 0 !important;\n        text-align: center !important;\n        line-height: 1.4 !important;\n        background: transparent !important;\n        border: none !important;\n    }\n\n    #job-tracker-dialog-container .loading-spinner {\n        width: 40px !important;\n        height: 40px !important;\n        border: 3px solid #e5e7eb !important;\n        border-top: 3px solid #3b82f6 !important;\n        border-radius: 50% !important;\n        animation: job-tracker-spin 1s linear infinite !important;\n        margin: 0 auto !important;\n        padding: 0 !important;\n        background: transparent !important;\n        display: block !important;\n        flex-shrink: 0 !important;\n        position: relative !important;\n        z-index: 1 !important;\n        box-sizing: border-box !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n        transform: none !important;\n        filter: none !important;\n        clip: auto !important;\n        overflow: visible !important;\n        min-width: 40px !important;\n        min-height: 40px !important;\n        max-width: 40px !important;\n        max-height: 40px !important;\n    }\n\n    /* Extra specific rule to ensure spinner is always visible */\n    #job-tracker-dialog-container .loading-state .loading-content .loading-spinner {\n        width: 40px !important;\n        height: 40px !important;\n        border: 3px solid #e5e7eb !important;\n        border-top: 3px solid #3b82f6 !important;\n        border-radius: 50% !important;\n        animation: job-tracker-spin 1s linear infinite !important;\n        display: block !important;\n        margin: 0 auto !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n    }\n\n    /* Success state */\n    #job-tracker-dialog-container .success {\n        border-color: #10b981;\n        background-color: #ecfdf5;\n    }\n\n    /* Error state */\n    #job-tracker-dialog-container .error {\n        border-color: #ef4444;\n        background-color: #fef2f2;\n    }\n\n    /* Spinner animation */\n    @keyframes job-tracker-spin {\n        from { transform: rotate(0deg); }\n        to { transform: rotate(360deg); }\n    }\n    `,n=document.createElement("style");n.textContent=t,document.head.appendChild(n)},R=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3;const r=document.getElementById("job-tracker-toast");r&&r.remove();const o=document.createElement("div");o.id="job-tracker-toast",o.className=`job-tracker-toast ${t}`;let a="";switch(t){case"success":a="background: #10b981 !important;";break;case"error":a="background: #ef4444 !important;";break;case"loading":a="background: #6366f1 !important;";break;default:a="background: #3b82f6 !important;"}o.style.cssText="\n        position: fixed !important;\n        top: 20px !important;\n        right: 20px !important;\n        left: auto !important;\n        z-index: 2147483647 !important;\n        width: 320px !important;\n        max-width: calc(100vw - 40px) !important;\n        min-width: 280px !important;\n        padding: 16px !important;\n        border-radius: 8px !important;\n        color: white !important;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;\n        font-size: 14px !important;\n        font-weight: normal !important;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;\n        transform: translateX(calc(100% + 20px)) !important;\n        transition: transform 0.3s ease-in-out !important;\n        display: flex !important;\n        align-items: center !important;\n        gap: 12px !important;\n        margin: 0 !important;\n        border: none !important;\n        outline: none !important;\n        opacity: 1 !important;\n        visibility: visible !important;\n        pointer-events: auto !important;\n        overflow: visible !important;\n        box-sizing: border-box !important;\n        line-height: 1.4 !important;\n        text-align: left !important;\n    "+a;let i="",l="";switch(t){case"loading":i='<div class="job-tracker-toast-spinner"></div>',l="Processing...";break;case"success":i='<svg class="job-tracker-toast-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',l="Success";break;case"error":i='<svg class="job-tracker-toast-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',l="Error";break;default:i='<svg class="job-tracker-toast-icon" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>',l="Info"}return o.innerHTML=`\n        ${i}\n        <div class="job-tracker-toast-content">\n            <div class="job-tracker-toast-title">${l}</div>\n            <div class="job-tracker-toast-message">${e}</div>\n        </div>\n    `,document.body.appendChild(o),setTimeout((()=>{o.classList.add("show"),o.style.transform="translateX(0) !important"}),100),"loading"!==t&&n>0&&setTimeout((()=>{o.classList.remove("show"),setTimeout((()=>{o.parentNode&&o.remove()}),300)}),n),o};window.showJobTrackerToast=R;let M="";const O=async()=>{M=await(async()=>await P(C,_)||_)()||"Control+F11",document.addEventListener("keydown",(e=>{var t;if("chrome-extension:"===window.location.protocol||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||"true"===(null===(t=e.target)||void 0===t?void 0:t.contentEditable))return;`${e.ctrlKey?"Control+":""}${e.shiftKey?"Shift+":""}${e.altKey?"Alt+":""}${e.key}`===M&&(e.preventDefault(),z())})),chrome.runtime.onMessage.addListener(((e,t,n)=>{if(console.log("Global message listener received:",e),"HOTKEY_UPDATED"===e.type&&(M=e.hotkey,console.log("Hotkey updated to:",M)),"SHOW_TOAST"===e.type&&R(e.message,e.toastType||"info",e.duration),"SHOW_DIALOG"===e.type)return console.log("Global listener: Showing dialog..."),z(),n({success:!0}),!0}))};"chrome-extension:"===window.location.protocol?console.log("[Job Tracker] Skipping hotkey listener inside extension UI"):O(),window.contentScriptLoaded=!0,window.runTracker=function(){z()},console.log("Job Tracker Content Script Loaded")})()})();
//# sourceMappingURL=content.js.map