import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, ThemeToggle, Input } from "./ui";
import { DEFAULTS } from "../constants";
import { getServerUrl, getAiServerUrl, saveServerUrl, saveAiServerUrl } from "../utils/storage";
import { Toast } from "./ui/Toast";

const Settings = () => {
    const [isOpenDialog, setIsOpenDialog] = useState(false);
    const [serverUrl, setServerUrl] = useState('');
    const [aiServerUrl, setAiServerUrl] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

    // Load current settings
    useEffect(() => {
        const loadSettings = async () => {
            try {
                const currentServerUrl = await getServerUrl();
                const currentAiServerUrl = await getAiServerUrl();
                setServerUrl(currentServerUrl);
                setAiServerUrl(currentAiServerUrl);
            } catch (error) {
                console.error('Error loading settings:', error);
            }
        };

        if (isOpenDialog) {
            loadSettings();
        }
    }, [isOpenDialog]);

    const handleSave = async () => {
        setIsLoading(true);
        try {
            await saveServerUrl(serverUrl);
            await saveAiServerUrl(aiServerUrl);
            setToast({ message: 'Settings saved successfully!', type: 'success' });
            setTimeout(() => {
                setIsOpenDialog(false);
                setToast(null);
            }, 1500);
        } catch (error) {
            console.error('Error saving settings:', error);
            setToast({ message: 'Failed to save settings. Please check URLs are valid.', type: 'error' });
        } finally {
            setIsLoading(false);
        }
    };

    const handleReset = () => {
        setServerUrl(DEFAULTS.serverUrl);
        setAiServerUrl(DEFAULTS.aiServerUrl);
    };

    return (
        <>
            <Button
                variant="ghost"
                size="sm"
                className="absolute top-6 right-6 text-gray-600 hover:text-gray-800 hover:bg-white/50 backdrop-blur-sm rounded-lg"
                onClick={() => setIsOpenDialog(true)}
                aria-label="Open settings"
            >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span className="text-xs">Settings</span>
            </Button>

            {isOpenDialog && (
                <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
                    <Card variant="elevated" padding="none" className="max-w-lg w-full shadow-2xl animate-in fade-in zoom-in duration-200">
                        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <h2 className="text-lg font-semibold text-white">Extension Settings</h2>
                                </div>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setIsOpenDialog(false)}
                                    className="text-white hover:bg-white/20 rounded-full p-1"
                                    aria-label="Close settings"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </Button>
                            </div>
                        </div>

                        <div className="p-6 space-y-6">
                            {/* Server Configuration Section */}
                            <div>
                                <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                                    <svg className="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                                    </svg>
                                    Server Configuration
                                </h3>

                                <div className="space-y-4">
                                    {/* Main Server URL */}
                                    <div className="bg-gray-50 rounded-xl p-4">
                                        <label className="block text-sm font-medium text-gray-900 mb-2">
                                            Main Server URL
                                        </label>
                                        <Input
                                            type="url"
                                            value={serverUrl}
                                            onChange={(e) => setServerUrl(e.target.value)}
                                            placeholder="http://192.168.13.99:8001/api/extension"
                                            className="w-full"
                                        />
                                        <p className="text-xs text-gray-500 mt-1">
                                            URL for the main job tracker API server
                                        </p>
                                    </div>

                                    {/* AI Server URL */}
                                    <div className="bg-gray-50 rounded-xl p-4">
                                        <label className="block text-sm font-medium text-gray-900 mb-2">
                                            AI Server URL (Ollama)
                                        </label>
                                        <Input
                                            type="url"
                                            value={aiServerUrl}
                                            onChange={(e) => setAiServerUrl(e.target.value)}
                                            placeholder="http://192.168.13.221:11434"
                                            className="w-full"
                                        />
                                        <p className="text-xs text-gray-500 mt-1">
                                            URL for the Ollama AI server for job parsing
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Appearance Section */}
                            <div className="border-t pt-6">
                                <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                                    <svg className="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                                    </svg>
                                    Appearance
                                </h3>
                                <div className="bg-gray-50 rounded-xl p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <span className="text-sm font-medium text-gray-900">Theme</span>
                                            <p className="text-xs text-gray-500">Choose your preferred theme</p>
                                        </div>
                                        <ThemeToggle variant="button" size="sm" />
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="border-t pt-6 flex justify-between items-center">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleReset}
                                    className="text-gray-600 hover:text-gray-800"
                                >
                                    Reset to Defaults
                                </Button>
                                <div className="flex space-x-2">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setIsOpenDialog(false)}
                                        className="text-gray-600 hover:text-gray-800"
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        variant="primary"
                                        size="sm"
                                        onClick={handleSave}
                                        disabled={isLoading}
                                        className="min-w-[80px]"
                                    >
                                        {isLoading ? (
                                            <div className="flex items-center space-x-2">
                                                <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                                                <span>Saving...</span>
                                            </div>
                                        ) : (
                                            'Save'
                                        )}
                                    </Button>
                                </div>
                            </div>

                            {/* Toast Notification */}
                            {toast && (
                                <div className={`fixed bottom-4 right-4 p-3 rounded-lg shadow-lg z-50 ${
                                    toast.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                                }`}>
                                    {toast.message}
                                </div>
                            )}
                        </div>
                    </Card>
                </div>
            )}
        </>
    )
};

export default Settings;