@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 211 100% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5% 64.9%;

    --radius: 0.75rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 211 100% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Chrome Extension Custom Styles */
@layer components {
  .job-tracker-dialog {
    @apply fixed z-50 rounded-xl shadow-xl bg-white/95 backdrop-blur-sm border border-gray-200 transition-all duration-300 animate-fade-in p-4 max-w-md;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
  }

  .job-tracker-dialog-header {
    @apply flex items-center justify-between border-b border-gray-200 pb-2 mb-3;
  }

  .job-tracker-handle {
    @apply cursor-move w-full;
  }

  .job-tracker-close {
    @apply p-1 rounded-full hover:bg-gray-200 transition-colors;
  }

  .form-field {
    @apply flex flex-col space-y-1 mb-4;
  }

  .input-label {
    @apply text-sm font-medium text-gray-700;
  }

  .input-field {
    @apply p-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all;
  }

  .save-button {
    @apply w-full bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-lg transition-colors font-medium;
  }

  .chip {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
  }

  .account-item {
    @apply p-3 rounded-lg border border-gray-200 mb-2 hover:bg-gray-50 transition-all cursor-pointer flex items-center justify-between;
  }

  .account-item.selected {
    @apply border-primary bg-primary/5;
  }
}